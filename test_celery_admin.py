#!/usr/bin/env python3
"""
测试Celery管理界面的脚本
"""

import requests
import json
from datetime import datetime

# 配置
BASE_URL = "http://localhost:8001"
ADMIN_EMAIL = "<EMAIL>"  # 需要替换为实际的管理员邮箱
ADMIN_PASSWORD = "admin123"  # 需要替换为实际的管理员密码

def test_admin_login():
    """测试管理员登录"""
    print("Testing admin login...")
    
    # 获取登录页面
    response = requests.get(f"{BASE_URL}/admin/login")
    print(f"Login page status: {response.status_code}")
    
    # 尝试登录
    login_data = {
        "username": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/admin/login", data=login_data, allow_redirects=False)
    print(f"Login response status: {response.status_code}")
    
    if response.status_code == 303:
        print("Login successful!")
        # 获取cookie
        cookies = response.cookies
        return cookies
    else:
        print("Login failed!")
        return None

def test_celery_tasks_page(cookies):
    """测试Celery任务页面"""
    print("\nTesting Celery tasks page...")
    
    response = requests.get(f"{BASE_URL}/admin/celery-tasks", cookies=cookies)
    print(f"Celery tasks page status: {response.status_code}")
    
    if response.status_code == 200:
        print("Celery tasks page loaded successfully!")
        # 检查页面内容
        if "Failed Celery Tasks" in response.text:
            print("Page contains expected content")
        else:
            print("Page content may be incorrect")
    else:
        print("Failed to load Celery tasks page")

def test_dashboard_with_celery_stats(cookies):
    """测试仪表板是否显示Celery统计信息"""
    print("\nTesting dashboard with Celery stats...")
    
    response = requests.get(f"{BASE_URL}/admin", cookies=cookies)
    print(f"Dashboard status: {response.status_code}")
    
    if response.status_code == 200:
        if "Failed Tasks" in response.text:
            print("Dashboard shows Celery task statistics!")
        else:
            print("Dashboard may not be showing Celery stats")
    else:
        print("Failed to load dashboard")

def main():
    """主测试函数"""
    print("Starting Celery Admin Interface Tests")
    print("=" * 50)
    
    # 测试登录
    cookies = test_admin_login()
    
    if cookies:
        # 测试Celery任务页面
        test_celery_tasks_page(cookies)
        
        # 测试仪表板
        test_dashboard_with_celery_stats(cookies)
    else:
        print("Cannot proceed with tests - login failed")
        print("Please ensure:")
        print("1. The application is running on http://localhost:8001")
        print("2. You have admin credentials set up")
        print("3. Update ADMIN_EMAIL and ADMIN_PASSWORD in this script")

if __name__ == "__main__":
    main()
