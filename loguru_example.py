from loguru import logger
import os
from datetime import datetime

# Remove default logger
logger.remove()


# Create logs directory if it doesn't exist
os.makedirs("logs", exist_ok=True)

# Add a new sink to write logs to a file
# Format: {time} {level} {message}
# Rotation: Every hour with datetime-based filename
log_path = "logs/app_{time:YYYY-MM-DD_HH}.log"
symlink_path = "logs/app.log"

# Remove symlink if it exists
if os.path.islink(symlink_path):
    os.unlink(symlink_path)

logger.add(
    log_path,
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
    level="INFO",
    rotation="1h",
    compression="zip",
    enqueue=True,  # Thread-safe writing
)

# Create symlink to current log file
current_log = f"app_{datetime.now().strftime('%Y-%m-%d_%H')}.log"
current_log_full_path = os.path.join("logs", current_log)

try:
    # Always remove old symlink if it exists
    if os.path.exists(symlink_path):
        os.remove(symlink_path)
    # Create new symlink with relative path
    os.symlink(current_log, symlink_path)
except Exception as e:
    logger.error(f"Failed to create symlink: {e}")

# Example usage
logger.info("This is an info message")
logger.error("This is an error message")
logger.debug("This debug message won't be logged since level is set to INFO")

try:
    1 / 0
except Exception as e:
    logger.exception("An error occurred")
