import logging
from contextlib import contextmanager
from typing import Generator

from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session, sessionmaker

from app.db.db import engine

# 配置日志记录器
logger = logging.getLogger(__name__)

# 配置会话工厂，添加连接池设置
SessionLocal = sessionmaker(bind=engine)


def get_db() -> Generator[Session, None, None]:
    """
    创建数据库会话的依赖函数。

    用法:
        @app.get("/items/")
        def read_items(db: Session = Depends(get_db)):
            ...

    Yields:
        Session: SQLAlchemy 会话实例

    Raises:
        SQLAlchemyError: 当数据库操作发生错误时
    """
    db = SessionLocal()
    try:
        logger.debug("创建新的数据库会话")
        yield db
    except SQLAlchemyError as e:
        logger.error(f"数据库会话发生错误: {str(e)}")
        raise
    finally:
        logger.debug("关闭数据库会话")
        db.close()


@contextmanager
def get_db_context() -> Generator[Session, None, None]:
    """
    创建一个上下文管理器用于手动管理数据库会话。

    用法:
        with get_db_context() as db:
            db.query(User).all()

    Yields:
        Session: SQLAlchemy 会话实例

    Raises:
        SQLAlchemyError: 当数据库操作发生错误时
    """
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except SQLAlchemyError as e:
        logger.error(f"数据库事务发生错误: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()


def check_db_health() -> bool:
    """
    检查数据库连接的健康状态。

    Returns:
        bool: 如果数据库连接正常则返回 True，否则返回 False
    """
    try:
        with get_db_context() as db:
            db.execute("SELECT 1")
        return True
    except SQLAlchemyError as e:
        logger.error(f"数据库健康检查失败: {str(e)}")
        return False
