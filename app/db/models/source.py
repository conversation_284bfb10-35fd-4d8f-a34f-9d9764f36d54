from enum import Enum as PyEnum
from typing import Any, <PERSON><PERSON><PERSON>, Dict, Optional

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Enum, String
from sqlalchemy.orm import Mapped, mapped_column

from . import Base


class SourceProviderType(PyEnum):
    rss = 1
    website = 2
    email = 3


class SourceSubProviderType(PyEnum):
    # default
    default = 1
    # rss
    wx_mp = 2
    # website
    github = 3
    hacker_news = 4
    google_news = 5


class SourceProvider(Base):
    """
    底层数据源，多用户共享，去重
    """

    __tablename__ = "source_provider"

    id: Mapped[int] = mapped_column(primary_key=True)
    source_type: Mapped[SourceProviderType] = mapped_column(
        Enum(SourceProviderType, name="source_type"), nullable=False
    )
    source_sub_type: Mapped[SourceSubProviderType] = mapped_column(
        Enum(SourceSubProviderType, name="source_sub_type"),
        default=SourceSubProviderType.default,
        nullable=False,
    )
    nickname: Mapped[str] = mapped_column(String, unique=True)
    keywords: Mapped[
        Optional[str]
    ]  # used for built-in news websites like google news, comma seperated
    icon_url: Mapped[Optional[str]]

    # email
    imap_server: Mapped[Optional[str]]
    imap_port: Mapped[Optional[int]]
    user: Mapped[Optional[str]]
    password: Mapped[Optional[str]]

    # rss, website...
    url: Mapped[Optional[str]]

class UserSource(Base):
    """
    支持多用户
    支持树型结构的 Source 管理
    """

    __tablename__ = "user_source"

    id: Mapped[int] = mapped_column(primary_key=True)
    user_id: Mapped[int] = mapped_column(index=True)
    inner_source_id: Mapped[int]
    nickname: Mapped[str]

    is_folder: Mapped[bool] = mapped_column(default=False)
    parent_id: Mapped[int] = mapped_column(default=0)
    sort_order: Mapped[int] = mapped_column(default=0)
    created_at: Mapped[int] = mapped_column(BigInteger)


class HackerNewsSource(Base):
    __tablename__ = "hacker_news_source"

    id: Mapped[int] = mapped_column(primary_key=True)
    source_id: Mapped[int]
    story_id: Mapped[int] = mapped_column(unique=True)
    extracted: Mapped[bool] = mapped_column(default=False)
    title: Mapped[str]
    author: Mapped[str]
    content: Mapped[Optional[str]]
    out_link: Mapped[Optional[str]]
    comments: Mapped[Optional[str]]  # comments can have sub comments
    score: Mapped[Optional[int]]
    ts: Mapped[Optional[int]]


class HackerNewsSourceFailedToExtract(Base):
    __tablename__ = "hacker_news_source_failed_to_extract"

    story_id: Mapped[int] = mapped_column(primary_key=True)
    source_id: Mapped[int]


class WebsiteSource(Base):
    __tablename__ = "website_source"

    id: Mapped[int] = mapped_column(primary_key=True)
    url: Mapped[str]
    source_id: Mapped[int]
    extracted: Mapped[bool]
    ts: Mapped[Optional[int]]


class GithubRepoSource(Base):
    __tablename__ = "github_repo_source"

    id: Mapped[int] = mapped_column(primary_key=True)
    url: Mapped[str] = mapped_column(String, unique=True)
    source_id: Mapped[int]
    extracted: Mapped[bool]
    ts: Mapped[Optional[int]]


class RssSource(Base):
    __tablename__ = "rss_source"

    id: Mapped[int] = mapped_column(primary_key=True)
    entry_id: Mapped[str]
    link: Mapped[str] = mapped_column(String, unique=True)
    title: Mapped[str]
    source_id: Mapped[int]
    extracted: Mapped[bool]
    ts: Mapped[Optional[int]]


class EmailSource(Base):
    __tablename__ = "email_source"

    id: Mapped[int] = mapped_column(primary_key=True)
    source_id: Mapped[int]
    subject: Mapped[str]
    sender: Mapped[str]
    sender_address: Mapped[str]
    ts: Mapped[int]
    content_html: Mapped[str]
    extracted: Mapped[bool]


class EmailSourceFailedToExtract(Base):
    __tablename__ = "email_source_failed_to_extract"

    id: Mapped[int] = mapped_column(primary_key=True)
    source_id: Mapped[int]
