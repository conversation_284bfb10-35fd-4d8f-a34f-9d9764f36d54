from typing import Any, List, Optional, Dict, ClassVar
from pgvector.sqlalchemy import Vector
from sqlalchemy import ARRAY, String
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column

from . import Base


class IncompleteArticle(Base):
    __tablename__ = "incomplete_article"

    id: Mapped[int] = mapped_column(primary_key=True)
    depth: Mapped[int]
    source_id: Mapped[int]
    origin_id: Mapped[int]
    headline: Mapped[Optional[str]]
    blurb: Mapped[Optional[str]]
    link: Mapped[Optional[str]] = mapped_column(unique=True)
    expanded: Mapped[bool]
    ts: Mapped[Optional[int]]


class CompleteArticle(Base):
    __tablename__ = "complete_article"

    id: Mapped[int] = mapped_column(primary_key=True)
    origin_url: Mapped[str]
    source_id: Mapped[int]
    origin_id: Mapped[int]
    title: Mapped[str]
    content: Mapped[Optional[str]]  # maybe a repo or pdf
    summarized: Mapped[bool]
    ts: Mapped[Optional[int]]


# TODO: consider merge summarized_article and complete_article into one table
class SummarizedArticle(Base):
    __tablename__ = "summarized_article"

    id: Mapped[int] = mapped_column(primary_key=True)
    source_id: Mapped[int]
    titles: Mapped[List[str]] = mapped_column(ARRAY(String))
    tags: Mapped[str]
    entities: Mapped[List[Any]] = mapped_column(ARRAY(JSONB))
    short_summaries: Mapped[List[str]] = mapped_column(ARRAY(String))
    style: Mapped[str]
    arguments: Mapped[List[str]] = mapped_column(ARRAY(String))
    summary: Mapped[str]
    embedding_vector: Mapped[Optional[List[float]]] = mapped_column(Vector(1536))
    ts: Mapped[Optional[int]]


class ArticleTopicRelation(Base):
    __tablename__ = "article_topic_relation"

    article_id: Mapped[int] = mapped_column(primary_key=True)
    topic_id: Mapped[int] = mapped_column(primary_key=True)


class TopicContent(Base):
    """
    Aggregated summary of a topic from multiple summarized articles
    """

    __tablename__ = "topic_content"

    id: Mapped[int] = mapped_column(primary_key=True)
    filter_character_id: Mapped[int] = mapped_column(index=True)
    title: Mapped[str]
    summary: Mapped[str]
    summary_zh: Mapped[str|None]
    image_url: Mapped[str] = mapped_column(default="")
    embedding_vector: Mapped[List[float]] = mapped_column(Vector(1536))
    need_details: Mapped[bool] = mapped_column(default=False)
    wanted: Mapped[bool] = mapped_column(
        default=False
    )  # wanted by filters, maybe more precison filter result in the future
    ts: Mapped[Optional[int]]


class TopicContentDetail(Base):
    """
    Expanded content of topic that been filtered, directly display to user
    """

    __tablename__ = "topic_content_detail"

    topic_id: Mapped[int] = mapped_column(primary_key=True)
    title: Mapped[str]
    blurb: Mapped[str]
    content: Mapped[str]
    ts: Mapped[Optional[int]]
