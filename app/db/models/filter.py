from enum import Enum as PyEnum
from typing import List, Optional
from sqlalchemy import  Index, null
from sqlalchemy.dialects.postgresql import ARRAY,ENUM,INTEGER,TEXT,TIMESTAMP
from sqlalchemy.orm import Mapped, mapped_column
from datetime import datetime

from . import Base


class ProcessStatus(PyEnum):
    pending = 1
    processing = 2
    success = 3
    failed = 4
    archived = 5


class ArticleProcessStatus(Base):
    """Tracks the processing status of articles for specific topics.

    This model uses a composite primary key consisting of source_id, article_id, and topic_id
    to uniquely identify each article processing status entry.
    """

    __tablename__ = "article_process_status"

    source_id: Mapped[int] = mapped_column(INTEGER, primary_key=True)
    article_id: Mapped[int] = mapped_column(INTEGER, primary_key=True)
    topic_id: Mapped[int] = mapped_column(INTEGER, primary_key=True)

    status: Mapped[ProcessStatus] = mapped_column(
        ENUM(ProcessStatus, name="process_status"),
        nullable=False,
        default=ProcessStatus.pending,
        index=True,
    )
    retry_count: Mapped[int] = mapped_column(default=0)
    last_processed_at: Mapped[datetime | None] = mapped_column(TIMESTAMP, nullable=True)
    error_log: Mapped[str | None] = mapped_column(TEXT, nullable=True)

    __table_args__ = (
        # Add indexes for better query performance
        Index("ix_article_process_source_article", "source_id", "article_id"),
        Index("ix_article_process_topic", "topic_id"),
        Index("ix_article_process_status", "status"),
    )

    def __repr__(self) -> str:
        """Return a string representation of the ArticleProcessStatus."""
        return (
            f"ArticleProcessStatus(source_id={self.source_id}, "
            f"article_id={self.article_id}, topic_id={self.topic_id}, "
            f"status={self.status.name})"
        )

    @property
    def is_completed(self) -> bool:
        """Check if the article processing is completed (either success or archived)."""
        return self.status in (ProcessStatus.success, ProcessStatus.archived)

    @property
    def can_retry(self) -> bool:
        """Check if the article can be retried for processing."""
        return self.status == ProcessStatus.failed and (
            self.retry_count is None or self.retry_count < 3
        )


class FilterCharacterSetting(Base):
    __tablename__ = "topic_filter"

    id: Mapped[int] = mapped_column(primary_key=True)
    user_id: Mapped[int] = mapped_column(unique=True,nullable=False)
    name: Mapped[str] = mapped_column(nullable=False)
    source_ids: Mapped[List[int]] = mapped_column(ARRAY(INTEGER), nullable=False)
    filters: Mapped[List[int]] = mapped_column(ARRAY(INTEGER))
    post_process_prompt: Mapped[Optional[str]]

    __table_args__ = (Index("ix_filter_sources", source_ids, postgresql_using="gin"),)


class FilterType(PyEnum):
    prompt = 1
    keywords = 2


class Filter(Base):
    """过滤器类，用于定义内容过滤的规则和设置。

    该类定义了两种过滤类型：
    1. 基于提示词的过滤 (prompt)
    2. 基于关键词的过滤 (keywords)

    每个过滤器都可以配置自己的 API 设置和模型参数。
    """

    __tablename__ = "filter"

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str]
    filter_type: Mapped[FilterType] = mapped_column(
        ENUM(FilterType, name="filter_type"), default=FilterType.prompt, nullable=False
    )
    prompt: Mapped[Optional[str]]
    need_details: Mapped[bool] = mapped_column(default=False)
    model: Mapped[str]
    api_base: Mapped[Optional[str]]
    api_key: Mapped[Optional[str]]

    def __str__(self) -> str:
        """返回过滤器的字符串表示。"""
        return f"Filter(id={self.id}, name='{self.name}', type={self.filter_type.name})"

    def __repr__(self) -> str:
        """返回过滤器的详细表示。"""
        return (
            f"Filter(id={self.id}, name='{self.name}', "
            f"type={self.filter_type.name}, model='{self.model}', "
            f"need_details={self.need_details})"
        )

    def validate(self) -> bool:
        """验证过滤器配置的有效性。

        Returns:
            bool: 配置有效返回 True，否则返回 False
        """
        if not self.name or not self.model:
            return False

        if self.filter_type == FilterType.prompt and not self.prompt:
            return False

        return True
