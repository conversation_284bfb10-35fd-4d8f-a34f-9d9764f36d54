from dotenv import load_dotenv
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

from app.db.models import Base

load_dotenv()

engine = create_engine(
    "postgresql+psycopg://liam@127.0.0.1:5432/liam",
    connect_args={"options": "-csearch_path=feeds,public"},
    pool_size=20,  # 连接池大小
    max_overflow=10,  # 最大溢出连接数
    pool_timeout=30,  # 连接池超时时间
    pool_recycle=3600,  # 连接回收时间（1小时）
    pool_pre_ping=True,  # 启用连接健康检查
    poolclass=QueuePool,  # 使用队列池
)


Base.metadata.create_all(bind=engine)
