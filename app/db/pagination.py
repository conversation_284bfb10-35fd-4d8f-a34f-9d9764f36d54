from enum import Enum
from typing import TypeVar, Generic, List, Optional, Type
from sqlalchemy import select, Column, desc, asc
from sqlalchemy.orm import Session, DeclarativeBase


# 定义分页方向枚举
class PaginationDirection(str, Enum):
    NEXT = "next"
    PREV = "prev"


# 通用类型变量,用于表示SQLAlchemy模型
T = TypeVar("T", bound=DeclarativeBase)


class PaginationResult(Generic[T]):
    """分页结果包装类"""

    def __init__(self, items: List[T], has_more: bool):
        self.items = items
        self.has_more = has_more


async def paginate_query(
    db: Session,
    model: Type[T],
    source_id: int,
    limit: int,
    cursor: Optional[int] = None,
    direction: PaginationDirection = PaginationDirection.NEXT,
    id_column: Optional[Column] = None,
    source_id_column: Optional[Column] = None,
) -> PaginationResult[T]:
    """
    通用的基于游标的分页查询函数

    Args:
        db: 数据库会话
        model: SQLAlchemy模型类
        source_id: source ID
        limit: 每页数量限制
        cursor: 游标位置(上一页最后一条记录的ID)
        direction: 分页方向(next或prev)
        id_column: 自定义ID列(默认为model.id)
        source_id_column: 自定义source_id列(默认为model.source_id)

    Returns:
        PaginationResult: 包含查询结果和是否有更多数据的信息

    Raises:
        ValueError: 参数验证失败时抛出
    """
    if limit <= 0:
        raise ValueError("Limit must be greater than 0")

    try:
        # 使用传入的列或默认列
        id_col = id_column or model.id
        source_id_col = source_id_column or model.source_id

        # 构建基础查询
        base_query = select(model).where(source_id_col == source_id)

        # 根据cursor和direction构建查询条件
        if cursor is None:
            stmt = base_query.order_by(desc(id_col)).limit(limit + 1)
        else:
            if direction == PaginationDirection.NEXT:
                stmt = (
                    base_query.where(id_col < cursor)
                    .order_by(desc(id_col))
                    .limit(limit + 1)
                )
            else:
                stmt = (
                    base_query.where(id_col > cursor)
                    .order_by(asc(id_col))
                    .limit(limit + 1)
                )

        # 执行查询
        results = list(db.execute(stmt).scalars().all())

        # 检查是否有更多数据
        has_more = len(results) > limit
        if has_more:
            results = results[:limit]

        # 根据方向处理结果顺序
        if direction == PaginationDirection.PREV:
            results.reverse()

        return PaginationResult(items=results, has_more=has_more)

    except Exception as e:
        # 记录错误并重新抛出
        raise ValueError(f"Error during pagination: {str(e)}") from e
