import asyncio
from app.logging import get_logger

_background_tasks = set()

logger = get_logger()

def add_background_task(coro):
    """Add a background task to be managed by the framework."""
    task = asyncio.create_task(coro)
    _background_tasks.add(task)
    task.add_done_callback(_background_tasks.discard)
    return task


async def cleanup_background_tasks():
    """Cancel and clean up all background tasks."""
    if not _background_tasks:
        return

    logger.info(f"Cleaning up {len(_background_tasks)} background tasks")
    for task in _background_tasks:
        if not task.done():
            task.cancel()

    await asyncio.gather(*_background_tasks, return_exceptions=True)
    _background_tasks.clear()


async def shutdown():
    """Handle shutdown events."""
    logger.info("Shutting down background tasks")
    await cleanup_background_tasks()
