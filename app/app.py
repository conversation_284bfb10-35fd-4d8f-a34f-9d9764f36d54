# Third-party imports
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON>Auth2<PERSON>ass<PERSON>R<PERSON>questForm
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates
from pathlib import Path
import os

from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.db import get_db
import app.db.models.user as models
import app.schemas as schemas
import app.util.auth as auth
from datetime import timedelta
 

# Local application imports
from app.logging import get_logger
from app.on_event import shutdown, add_background_task
from app.api import api_router
from app.admin_web import web_router as admin_web_router
from app.middlewares import RequestIDMiddleware,LogFileMiddleWare
from app.api.utils import CommonDependencies

logger = get_logger()

async def lifespan(app: FastAPI):
    logger.info("Starting up FastAPI application")

    # Initialize background tasks
    tasks = [
        # Uncomment and add tasks as needed
        # ("Source sync", keep_sync_sources()),
    ]

    for task_name, task in tasks:
        logger.debug(f"Setting up background task: {task_name}")
        add_background_task(task)

    logger.info("Application startup complete")
    yield

    logger.info("Initiating application shutdown")
    await shutdown()
    logger.info("Application shutdown complete")


# Create FastAPI application
app = FastAPI(
    title="EyeSaver API",
    description="API for EyeSaver application",
    version="1.0.0",
    lifespan=lifespan,
)

# Configure static files
app.mount("/static", StaticFiles(directory=Path(__file__).parent / "static"), name="static")

# Configure CORS - allow cookies to be included in requests
# For production, replace "*" with the specific origins that should be allowed
origins = ["*"]
if "CORS_ORIGINS" in os.environ:
    origins = os.environ["CORS_ORIGINS"].split(",")

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,  # Important for cookies
    allow_methods=["*"],
    allow_headers=["*"],
)
app.add_middleware(RequestIDMiddleware)
app.add_middleware(LogFileMiddleWare)

# Include API routes
app.include_router(api_router)

# Include Admin Web routes
app.include_router(admin_web_router)

@app.post("/token", response_model=schemas.Token)
def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(), 
    db: Session = Depends(get_db),
    commons: CommonDependencies = Depends(CommonDependencies)
):
    user = db.query(models.User).filter(models.User.email == form_data.username).first()
    
    if not user or not auth.verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=commons.translate("auth.invalid_credentials"),
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Create access token with extended expiry time
    access_token_expires = timedelta(minutes=auth.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = auth.create_access_token(
        data={"sub": str(user.id)},
        expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}


