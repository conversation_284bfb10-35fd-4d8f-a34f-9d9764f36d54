import os
import sys
from pathlib import Path
from loguru import logger as _logger
from app.middlewares import get_request_id,get_log_file
from celery import current_task

# Get environment variables with defaults
LOG_LEVEL = os.getenv("LOG_LEVEL", "DEBUG")  # Default to DEBUG if not set
LOG_PATH = os.getenv("LOG_PATH", "logs")  # Default to 'logs' directory

def patch_request_id(record):
    api_req_id = get_request_id()
    if api_req_id:
        record["extra"]["request_id"] = api_req_id
    elif current_task:
        record["extra"]["request_id"] = current_task.request.id
    else:
        record["extra"]["request_id"] = "no-request-id"
    return record

# mkdir if not exists
log_dir = Path(LOG_PATH)
log_dir.mkdir(exist_ok=True)

_logger.remove()
if LOG_LEVEL == "DEBUG":
        console_format = (
            "<level>{level: <8}</level> | "
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<blue>{extra[request_id]}</blue> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
else:
        console_format = (
            "{level: <8} | "
            "{time:YYYY-MM-DD HH:mm:ss} | "
            "{extra[request_id]} | "
            "{name}:{function}:{line} | "
            "{message}"
        )
_logger.add(
        sys.stderr,
        format=console_format,
        filter=patch_request_id,
        level=LOG_LEVEL,
        colorize=True,
)

def setup_logging(file_name: str):
    # Add console handler with appropriate format and level
    # Use colors and extended format in debug mode
    # Get current timestamp for log file name
    if file_name == "":
        file_name = "stdout"
    log_path = str(log_dir / f"{file_name}.log")
    file_format = (
        "{level: <8} | "
        "{time:YYYY-MM-DD HH:mm:ss} | "
        "{extra[request_id]} | "
        "{name}:{function}:{line} | "
        "{message}"
    )
    _logger.add(
        log_path,
        format=file_format,
        filter=lambda record: get_log_file()==file_name,
        level=LOG_LEVEL,
        rotation="1h",
        retention="30 days",  # Keep logs for 30 days
    )

setup_logging("")
setup_logging("app")
setup_logging("celery")

def get_logger():
    return _logger.patch(patch_request_id)
