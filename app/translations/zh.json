{"welcome": "欢迎使用EyeSaver API", "user": {"not_found": "未找到用户", "created": "用户创建成功", "updated": "用户更新成功", "deleted": "用户删除成功", "already_registered": "用户名已被注册", "username_taken": "用户名已被占用"}, "auth": {"invalid_credentials": "用户名/邮箱或密码不正确", "access_denied": "访问被拒绝", "token_expired": "令牌已过期", "email_already_registered": "邮箱已被注册"}, "common": {"error": "发生错误", "success": "操作成功完成", "not_found": "未找到资源"}, "filter": {"required": "过滤器设置是必需的", "already_exists": "过滤器设置已存在", "not_found": "未找到过滤器设置", "invalid_source_ids": "无效的来源ID"}, "source": {"already_exists": "提供者 {name} 已存在", "not_found": "未找到提供者 {id}", "invalid_direction": "方向必须为'next'或'prev'"}, "topic": {"filter_id_required": "需要指定过滤器ID", "invalid_direction": "方向必须为'next'或'prev'", "not_found": "未找到主题"}}