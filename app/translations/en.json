{"welcome": "Welcome to EyeSaver API", "user": {"not_found": "User not found", "created": "User created successfully", "updated": "User updated successfully", "deleted": "User deleted successfully", "already_registered": "Username already registered", "username_taken": "Username already taken"}, "auth": {"invalid_credentials": "Incorrect username/email or password", "access_denied": "Access denied", "token_expired": "Token has expired", "email_already_registered": "Email already registered"}, "common": {"error": "An error occurred", "success": "Operation completed successfully", "not_found": "Resource not found"}, "filter": {"required": "FilterCharacterSetting is required", "already_exists": "FilterCharacterSetting already exists", "not_found": "FilterCharacterSetting not found", "invalid_source_ids": "Invalid source_ids"}, "source": {"already_exists": "Provider {name} already exists", "not_found": "Provider {id} not found", "invalid_direction": "Direction must be either 'next' or 'prev'"}, "topic": {"filter_id_required": "filter_id is required", "invalid_direction": "direction must be either 'next' or 'prev'", "not_found": "Topic not found"}}