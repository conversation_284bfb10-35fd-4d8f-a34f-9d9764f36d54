{% extends "admin/layout.html" %} {% block title %}Celery Tasks{% endblock %} {%
block head %}
<style>
  .task-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
  }

  .task-table th,
  .task-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }

  .task-table th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #374151;
  }

  .task-table tbody tr:hover {
    background-color: #f9fafb;
  }

  .status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .status-failure {
    background-color: #fee2e2;
    color: #dc2626;
  }

  .error-message {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .task-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .bulk-actions {
    background-color: #f9fafb;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    display: flex;
    gap: 1rem;
    align-items: center;
  }

  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
  }

  .pagination a,
  .pagination span {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    text-decoration: none;
    color: #374151;
  }

  .pagination a:hover {
    background-color: #f3f4f6;
  }

  .pagination .current {
    background-color: #3b82f6;
    color: white;
    border-color: #3b82f6;
  }

  .message {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
  }

  .message.success {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
  }

  .message.error {
    background-color: #fee2e2;
    color: #dc2626;
    border: 1px solid #fca5a5;
  }

  .badge {
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
    margin-left: 0.5rem;
  }

  .badge-warning {
    background-color: #fef3c7;
    color: #92400e;
  }

  .error-type {
    font-size: 0.8rem;
    color: #dc2626;
    margin-bottom: 0.25rem;
  }
</style>
{% endblock %} {% block content %}
<div class="header-with-actions">
  <h2>Failed Celery Tasks</h2>
  <div class="actions">
    <button onclick="location.reload()" class="btn btn-secondary">
      Refresh
    </button>
    <button onclick="showCleanupModal()" class="btn btn-warning">
      Cleanup Old Tasks
    </button>
  </div>
</div>

{% if request.query_params.get('message') %}
<div class="message success">{{ request.query_params.get('message') }}</div>
{% endif %}

<div class="stats-grid" style="margin-bottom: 1rem">
  <div class="stat-card">
    <h3>Failed Tasks</h3>
    <div class="number">{{ total_count }}</div>
  </div>
</div>

{% if failed_tasks %}
<form id="retry-form" method="post" action="/admin/celery-tasks/retry">
  <div class="bulk-actions">
    <label> <input type="checkbox" id="select-all" /> Select All </label>
    <button type="submit" class="btn btn-primary" id="retry-btn" disabled>
      Retry Selected Tasks
    </button>
    <span id="selected-count">0 tasks selected</span>
  </div>

  <div class="table-container">
    <table class="task-table">
      <thead>
        <tr>
          <th width="40">
            <input type="checkbox" id="header-checkbox" />
          </th>
          <th>Task Name</th>
          <th>Task ID</th>
          <th>Error Message</th>
          <th>Failed At</th>
          <th>Retries</th>
          <th>Worker</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for task in failed_tasks %}
        <tr>
          <td>
            <input
              type="checkbox"
              name="task_ids"
              value="{{ task.task_id }}"
              class="task-checkbox"
            />
          </td>
          <td>
            <strong>{{ task.name or 'Unknown Task' }}</strong>
            {% if not task.name %}
            <span class="badge badge-warning">No Name</span>
            {% endif %}
          </td>
          <td>
            <code style="font-size: 0.8rem">{{ task.task_id[:16] }}...</code>
          </td>
          <td>
            {% if task.error_type %}
            <div class="error-type">
              <strong>{{ task.error_type }}</strong>
            </div>
            {% endif %}
            <div class="error-message" title="{{ task.error_message }}">
              {{ task.error_message or 'No error message' }}
            </div>
          </td>
          <td>
            {% if task.date_done %} {{ task.date_done.strftime('%Y-%m-%d
            %H:%M:%S') }} {% else %} - {% endif %}
          </td>
          <td>{{ task.retries }}</td>
          <td>{{ task.worker or '-' }}</td>
          <td class="task-actions">
            <a
              href="/admin/celery-tasks/{{ task.task_id }}/detail"
              class="btn btn-sm btn-secondary"
              >View</a
            >
            <button
              type="button"
              onclick="retryTask('{{ task.task_id }}')"
              class="btn btn-sm btn-primary"
            >
              Retry
            </button>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</form>

<!-- Pagination -->
{% if total_pages > 1 %}
<div class="pagination">
  {% if current_page > 1 %}
  <a href="/admin/celery-tasks?page=1">First</a>
  <a href="/admin/celery-tasks?page={{ current_page - 1 }}">Previous</a>
  {% endif %} {% set start_page = current_page - 2 if current_page - 2 > 1 else
  1 %} {% set end_page = current_page + 2 if current_page + 2 < total_pages else
  total_pages %} {% for page_num in range(start_page, end_page + 1) %} {% if
  page_num == current_page %}
  <span class="current">{{ page_num }}</span>
  {% else %}
  <a href="/admin/celery-tasks?page={{ page_num }}">{{ page_num }}</a>
  {% endif %} {% endfor %} {% if current_page < total_pages %}
  <a href="/admin/celery-tasks?page={{ current_page + 1 }}">Next</a>
  <a href="/admin/celery-tasks?page={{ total_pages }}">Last</a>
  {% endif %}
</div>
{% endif %} {% else %}
<div class="empty-state">
  <h3>No Failed Tasks</h3>
  <p>All Celery tasks are running successfully!</p>
</div>
{% endif %}

<!-- Cleanup Modal -->
<div
  id="cleanup-modal"
  style="
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
  "
>
  <div
    style="
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      padding: 2rem;
      border-radius: 0.5rem;
      min-width: 400px;
    "
  >
    <h3>Cleanup Old Failed Tasks</h3>
    <p>
      This will permanently delete failed tasks older than the specified number
      of days.
    </p>

    <form id="cleanup-form" method="post" action="/admin/celery-tasks/cleanup">
      <div style="margin-bottom: 1rem">
        <label for="days_old">Delete tasks older than:</label>
        <select
          name="days_old"
          id="days_old"
          style="margin-left: 0.5rem; padding: 0.25rem"
        >
          <option value="1">1 day</option>
          <option value="3">3 days</option>
          <option value="7" selected>7 days</option>
          <option value="14">14 days</option>
          <option value="30">30 days</option>
        </select>
      </div>

      <div
        style="
          text-align: right;
          gap: 0.5rem;
          display: flex;
          justify-content: flex-end;
        "
      >
        <button
          type="button"
          onclick="hideCleanupModal()"
          class="btn btn-secondary"
        >
          Cancel
        </button>
        <button type="submit" class="btn btn-warning">Cleanup Tasks</button>
      </div>
    </form>
  </div>
</div>

<script>
  // 批量选择功能
  document.addEventListener("DOMContentLoaded", function () {
    const selectAllCheckbox = document.getElementById("select-all");
    const headerCheckbox = document.getElementById("header-checkbox");
    const taskCheckboxes = document.querySelectorAll(".task-checkbox");
    const retryBtn = document.getElementById("retry-btn");
    const selectedCount = document.getElementById("selected-count");

    function updateUI() {
      const checkedBoxes = document.querySelectorAll(".task-checkbox:checked");
      const count = checkedBoxes.length;

      selectedCount.textContent = `${count} tasks selected`;
      retryBtn.disabled = count === 0;

      // 更新全选状态
      if (count === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
        headerCheckbox.indeterminate = false;
        headerCheckbox.checked = false;
      } else if (count === taskCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
        headerCheckbox.indeterminate = false;
        headerCheckbox.checked = true;
      } else {
        selectAllCheckbox.indeterminate = true;
        headerCheckbox.indeterminate = true;
      }
    }

    // 全选功能
    selectAllCheckbox.addEventListener("change", function () {
      taskCheckboxes.forEach((checkbox) => {
        checkbox.checked = this.checked;
      });
      updateUI();
    });

    headerCheckbox.addEventListener("change", function () {
      taskCheckboxes.forEach((checkbox) => {
        checkbox.checked = this.checked;
      });
      updateUI();
    });

    // 单个选择
    taskCheckboxes.forEach((checkbox) => {
      checkbox.addEventListener("change", updateUI);
    });

    // 表单提交确认
    document
      .getElementById("retry-form")
      .addEventListener("submit", function (e) {
        const checkedBoxes = document.querySelectorAll(
          ".task-checkbox:checked"
        );
        if (checkedBoxes.length === 0) {
          e.preventDefault();
          alert("Please select at least one task to retry.");
          return;
        }

        if (
          !confirm(
            `Are you sure you want to retry ${checkedBoxes.length} tasks?`
          )
        ) {
          e.preventDefault();
        }
      });

    updateUI();
  });

  // 单个任务重试
  function retryTask(taskId) {
    if (confirm("Are you sure you want to retry this task?")) {
      const form = document.createElement("form");
      form.method = "POST";
      form.action = "/admin/celery-tasks/retry";

      const input = document.createElement("input");
      input.type = "hidden";
      input.name = "task_ids";
      input.value = taskId;

      form.appendChild(input);
      document.body.appendChild(form);
      form.submit();
    }
  }

  // 清理模态框功能
  function showCleanupModal() {
    document.getElementById("cleanup-modal").style.display = "block";
  }

  function hideCleanupModal() {
    document.getElementById("cleanup-modal").style.display = "none";
  }

  // 清理表单提交确认
  document
    .getElementById("cleanup-form")
    .addEventListener("submit", function (e) {
      const daysOld = document.getElementById("days_old").value;
      if (
        !confirm(
          `Are you sure you want to delete all failed tasks older than ${daysOld} days? This action cannot be undone.`
        )
      ) {
        e.preventDefault();
      }
    });

  // 点击模态框外部关闭
  document
    .getElementById("cleanup-modal")
    .addEventListener("click", function (e) {
      if (e.target === this) {
        hideCleanupModal();
      }
    });
</script>
{% endblock %}
