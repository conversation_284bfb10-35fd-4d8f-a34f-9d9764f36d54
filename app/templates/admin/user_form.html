{% extends "admin/layout.html" %}

{% block title %}{% if user %}Edit User: {{ user.username }}{% else %}Add New User{% endif %}{% endblock %}

{% block content %}
<div class="header-with-actions">
    <h2>{% if user %}Edit User{% else %}Add New User{% endif %}</h2>
</div>

<div class="form-container">
    {% if error %}
    <div class="form-error" style="margin-bottom: 1rem;">{{ error }}</div>
    {% endif %}
    
    <form method="post" action="{% if user %}/admin/users/{{ user.id }}/edit{% else %}/admin/users/new{% endif %}">
        <div class="form-group">
            <label for="username">Username</label>
            <input type="text" id="username" name="username" class="form-control" value="{{ user.username if user else '' }}" required>
        </div>
        
        <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" name="email" class="form-control" value="{{ user.email if user else '' }}" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password {% if user %}(leave blank to keep current){% endif %}</label>
            <input type="password" id="password" name="password" class="form-control" {% if not user %}required{% endif %}>
        </div>
        
        <div class="form-check">
            <input type="checkbox" id="is_admin" name="is_admin" class="form-check-input" {% if is_admin %}checked{% endif %}>
            <label for="is_admin">Admin User</label>
        </div>
        
        <div class="actions">
            <button type="submit" class="btn btn-primary">{% if user %}Update User{% else %}Create User{% endif %}</button>
            <a href="{% if user %}/admin/users/{{ user.id }}{% else %}/admin/users{% endif %}" class="btn btn-danger">Cancel</a>
        </div>
    </form>
</div>
{% endblock %} 