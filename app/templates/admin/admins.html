{% extends "admin/layout.html" %}

{% block title %}Admin Users{% endblock %}

{% block content %}
<div class="header-with-actions">
    <h2>Admin Users</h2>
</div>

<div class="table-container">
    <table class="admin-table">
        <thead>
            <tr>
                <th>ID</th>
                <th>Username</th>
                <th>Email</th>
                <th>Role</th>
                <th>Created At</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for admin in admins %}
            <tr>
                <td>{{ admin.id }}</td>
                <td>{{ admin.username }}</td>
                <td>{{ admin.email }}</td>
                <td>{{ admin.role }}</td>
                <td>{{ admin.created_at.strftime('%Y-%m-%d %H:%M') if admin.created_at else "" }}</td>
                <td class="actions">
                    <a href="/admin/users/{{ admin.user_id }}" class="btn btn-primary btn-sm">View User</a>
                    <form method="post" action="/admin/admins/{{ admin.id }}" hx-delete="/admin/admins/{{ admin.id }}" hx-confirm="Are you sure you want to remove admin privileges?" hx-target="closest tr" hx-swap="outerHTML">
                        <button type="submit" class="btn btn-danger btn-sm">Remove Admin</button>
                    </form>
                </td>
            </tr>
            {% endfor %}
            
            {% if admins|length == 0 %}
            <tr>
                <td colspan="6">No admin users found.</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<div class="admin-actions" style="margin-top: 2rem;">
    <h3>Add New Admin User</h3>
    <p>To add a new admin user, go to the <a href="/admin/users">Users</a> page, select a user, and click "Edit" to grant admin privileges.</p>
</div>
{% endblock %} 