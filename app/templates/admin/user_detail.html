{% extends "admin/layout.html" %}

{% block title %}User Details: {{ user.username }}{% endblock %}

{% block content %}
<div class="header-with-actions">
    <h2>User Details</h2>
    <div class="actions">
        <a href="/admin/users/{{ user.id }}/edit" class="btn btn-primary">Edit User</a>
        <form method="post" action="/admin/users/{{ user.id }}/delete" style="display: inline-block;">
            <button type="button" class="btn btn-danger" data-confirm="Are you sure you want to delete this user?">Delete User</button>
        </form>
    </div>
</div>

<div class="form-container">
    <div class="detail-grid">
        <div class="detail-item">
            <div class="detail-label">ID</div>
            <div class="detail-value">{{ user.id }}</div>
        </div>
        
        <div class="detail-item">
            <div class="detail-label">Username</div>
            <div class="detail-value">{{ user.username }}</div>
        </div>
        
        <div class="detail-item">
            <div class="detail-label">Email</div>
            <div class="detail-value">{{ user.email }}</div>
        </div>
        
        <div class="detail-item">
            <div class="detail-label">Admin Status</div>
            <div class="detail-value">
                {% if is_admin %}
                <span class="badge badge-primary">Admin</span>
                {% else %}
                <span>Regular User</span>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="actions" style="margin-top: 2rem;">
        <a href="/admin/users" class="btn btn-primary">Back to Users</a>
    </div>
</div>
{% endblock %} 