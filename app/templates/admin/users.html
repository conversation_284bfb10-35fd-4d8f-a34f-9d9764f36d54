{% extends "admin/layout.html" %}

{% block title %}Users Management{% endblock %}

{% block content %}
<div class="header-with-actions">
    <h2>Users Management</h2>
    <a href="/admin/users/new" class="btn btn-primary">Add User</a>
</div>

<div class="table-container">
    <table class="admin-table">
        <thead>
            <tr>
                <th>ID</th>
                <th>Username</th>
                <th>Email</th>
                <th>Admin</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for user in users %}
            <tr>
                <td>{{ user.id }}</td>
                <td>{{ user.username }}</td>
                <td>{{ user.email }}</td>
                <td>
                    {% if user.id in admin_user_ids %}
                    <span class="badge badge-primary">Admin</span>
                    {% endif %}
                </td>
                <td class="actions">
                    <a href="/admin/users/{{ user.id }}" class="btn btn-primary btn-sm">View</a>
                    <a href="/admin/users/{{ user.id }}/edit" class="btn btn-primary btn-sm">Edit</a>
                    <form method="post" action="/admin/users/{{ user.id }}/delete" style="display: inline-block;">
                        <button type="button" class="btn btn-danger btn-sm" data-confirm="Are you sure you want to delete this user?">Delete</button>
                    </form>
                </td>
            </tr>
            {% endfor %}
            
            {% if users|length == 0 %}
            <tr>
                <td colspan="5">No users found.</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>
{% endblock %} 