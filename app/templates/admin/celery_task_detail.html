{% extends "admin/layout.html" %}

{% block title %}Task Detail - {{ task.task_id }}{% endblock %}

{% block head %}
<style>
.task-detail {
    max-width: 1200px;
    margin: 0 auto;
}

.detail-section {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.detail-section h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 0.5rem;
}

.detail-grid {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.detail-label {
    font-weight: 600;
    color: #6b7280;
}

.detail-value {
    color: #374151;
    word-break: break-all;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-failure {
    background-color: #fee2e2;
    color: #dc2626;
}

.code-block {
    background-color: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-all;
}

.error-section {
    border-left: 4px solid #dc2626;
    background-color: #fef2f2;
}

.traceback-section {
    border-left: 4px solid #f59e0b;
    background-color: #fffbeb;
}

.args-kwargs {
    background-color: #f0f9ff;
    border-left: 4px solid #3b82f6;
}

.back-button {
    margin-bottom: 1rem;
}

.retry-section {
    text-align: center;
    padding: 1rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
    margin-top: 1rem;
}
</style>
{% endblock %}

{% block content %}
<div class="task-detail">
    <div class="back-button">
        <a href="/admin/celery-tasks" class="btn btn-secondary">← Back to Tasks</a>
    </div>

    <div class="header-with-actions">
        <h2>Task Detail</h2>
        <div class="actions">
            <button onclick="retryTask('{{ task.task_id }}')" class="btn btn-primary">Retry Task</button>
        </div>
    </div>

    <!-- Basic Information -->
    <div class="detail-section">
        <h3>Basic Information</h3>
        <div class="detail-grid">
            <div class="detail-label">Task ID:</div>
            <div class="detail-value"><code>{{ task.task_id }}</code></div>
            
            <div class="detail-label">Task Name:</div>
            <div class="detail-value">{{ task.name or 'Unknown' }}</div>
            
            <div class="detail-label">Status:</div>
            <div class="detail-value">
                <span class="status-badge status-failure">{{ task.status }}</span>
            </div>
            
            <div class="detail-label">Failed At:</div>
            <div class="detail-value">
                {% if task.date_done %}
                    {{ task.date_done.strftime('%Y-%m-%d %H:%M:%S UTC') }}
                {% else %}
                    -
                {% endif %}
            </div>
            
            <div class="detail-label">Worker:</div>
            <div class="detail-value">{{ task.worker or 'Unknown' }}</div>
            
            <div class="detail-label">Queue:</div>
            <div class="detail-value">{{ task.queue or 'default' }}</div>
            
            <div class="detail-label">Retries:</div>
            <div class="detail-value">{{ task.retries }}</div>
        </div>
    </div>

    <!-- Error Information -->
    {% if task.error_message or task.error_type %}
    <div class="detail-section error-section">
        <h3>Error Information</h3>
        {% if task.error_type %}
        <div class="detail-grid">
            <div class="detail-label">Error Type:</div>
            <div class="detail-value"><code>{{ task.error_type }}</code></div>
        </div>
        {% endif %}
        
        {% if task.error_message %}
        <div style="margin-top: 1rem;">
            <div class="detail-label" style="margin-bottom: 0.5rem;">Error Message:</div>
            <div class="code-block">{{ task.error_message }}</div>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- Traceback -->
    {% if task.traceback %}
    <div class="detail-section traceback-section">
        <h3>Traceback</h3>
        <div class="code-block">{{ task.traceback }}</div>
    </div>
    {% endif %}

    <!-- Arguments and Keyword Arguments -->
    {% if task.args or task.kwargs %}
    <div class="detail-section args-kwargs">
        <h3>Task Arguments</h3>
        
        {% if task.args %}
        <div style="margin-bottom: 1rem;">
            <div class="detail-label" style="margin-bottom: 0.5rem;">Positional Arguments:</div>
            <div class="code-block">{{ task.args | tojson(indent=2) }}</div>
        </div>
        {% endif %}
        
        {% if task.kwargs %}
        <div>
            <div class="detail-label" style="margin-bottom: 0.5rem;">Keyword Arguments:</div>
            <div class="code-block">{{ task.kwargs | tojson(indent=2) }}</div>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- Retry Section -->
    <div class="retry-section">
        <p>You can retry this task with the same arguments by clicking the button above.</p>
        <p><strong>Note:</strong> This will create a new task with the same parameters.</p>
    </div>
</div>

<script>
function retryTask(taskId) {
    if (confirm('Are you sure you want to retry this task?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/admin/celery-tasks/retry';
        
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'task_ids';
        input.value = taskId;
        
        form.appendChild(input);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
