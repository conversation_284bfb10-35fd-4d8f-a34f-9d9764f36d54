"""
Example of how to use the logging configuration in any module.
This is just a reference file to show the proper usage pattern.
"""

from app.logging import get_logger
logger = get_logger()

def example_function():
    # Different log levels
    logger.debug("Debug message - detailed information for debugging")
    logger.info("Info message - general information about program execution")
    logger.warning("Warning message - something unexpected but not error")
    logger.error("Error message - something failed but application still running")

    # Exception logging with traceback
    try:
        result = 1 / 0
    except Exception as e:
        logger.exception("Exception occurred with full traceback")

    # Structured logging with extra fields
    logger.info(
        "User action", extra={"user_id": "123", "action": "login", "ip": "***********"}
    )


def main():
    """
    Example of how logging appears in different scenarios.
    """
    logger.info("Starting example usage demonstration")

    # Function call that generates various log messages
    example_function()

    # Example of logging in different contexts
    for i in range(3):
        logger.debug(f"Processing item {i}")
        if i == 1:
            logger.warning(f"Item {i} requires attention")

    logger.info("Example usage demonstration completed")


if __name__ == "__main__":
    main()

"""
Usage:
1. Import the logger:
   from app.logging_config import logger

2. Use appropriate log levels:
   - logger.debug(): Detailed information for debugging
   - logger.info(): General information about program execution
   - logger.warning(): Something unexpected but not an error
   - logger.error(): Something failed but application still running
   - logger.critical(): Application failure requiring immediate attention
   - logger.exception(): Log exception with full traceback (use in except blocks)

3. Environment variables that affect logging:
   - LOG_LEVEL: Set to "DEBUG" or "INFO" (default: "DEBUG")
   - LOG_PATH: Directory for log files (default: "logs")

4. Log files:
   - Current log: logs/app.log (symlink to current hourly log)
   - Hourly logs: logs/app_YYYY-MM-DD_HH.log
   - Retention: 30 days
"""
