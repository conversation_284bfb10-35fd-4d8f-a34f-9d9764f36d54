import time
import j<PERSON>
from typing import List, Dict
from openai import OpenAI
from app.logging import get_logger
import asyncio

from app.util.dify_client.dify import (
    CompletionClient,
    WorkflowClient,
    UserModel,
    ResponseMode,
)
from app.util.dify_client.model import (
    ChunkCompletionResponseModel,
)

openai_client = OpenAI()
logger = get_logger()


def extract_insight(
    text: str,
) -> ChunkCompletionResponseModel:
    logger.info(
        f"LLM call to extract_insight at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}"
    )
    client = WorkflowClient(
        base_url="http://127.0.0.1/v1",
        api_key="app-5aP0FFzdBeU7nDLZ6P9IJdIB",
    )
    return client.run(user=UserModel("liam"),response_mode=ResponseMode.BLOCKING,inputs={"input": text})

def extract_newsletter(text: str) -> ChunkCompletionResponseModel:
    logger.info(
        f"LLM call to extract_newsletter at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}"
    )
    client = CompletionClient(
        base_url="http://127.0.0.1/v1",
        api_key="app-WsJmywU7OhydjnQpaEMJXzn3",
    )
    return client.create_completion_message(user=UserModel("liam"),response_mode=ResponseMode.BLOCKING,inputs={"query": text})

def vectorize(text: str) -> List[float]:
    logger.info(
        f"LLM call to vectorize at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}"
    )
    response = openai_client.embeddings.create(input=text,model="text-embedding-3-small")
    return response.data[0].embedding


def structure_summarize(text: str) -> ChunkCompletionResponseModel:
    logger.info(
        f"LLM call to structure_summarize at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}"
    )
    client = CompletionClient(
        base_url="http://************/v1",
        api_key="app-GX5nHxUzRamyE7FpO6SNllIW",
    )
    input = {
        "query": text,
    }
    return client.create_completion_message(inputs=input,response_mode=ResponseMode.BLOCKING,user=UserModel("liam"))


def same_news(t1: str, t2: str) -> ChunkCompletionResponseModel:
    logger.info(
        f"LLM call to same_news at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}"
    )
    client = CompletionClient(
        base_url="http://************/v1",
        api_key="app-Py5HttaCuz8DOwVuxI0eG08M",
    )
    input = {"query": f"news1: {t1} <|split> news2: {t2}"}
    return client.create_completion_message(inputs=input,response_mode=ResponseMode.BLOCKING,user=UserModel("liam"))


def extract_feed_info(content: str) -> Dict[str, str]:
    """
    Extract RSS/Atom feed information using LLM.

    Args:
        content: XML content from RSS/Atom feed

    Returns:
        Dictionary containing title, description, and link
    """
    # Prepare content for LLM - take first 20 lines and filter long lines
    lines = content.split("\n")[:20]
    filtered_lines = [line for line in lines if len(line) < 500]
    content_for_llm = "\n".join(filtered_lines)

    prompt = f"""Given the following XML content from an RSS/Atom feed, extract the title, description, and link.
If you can't find exact matches, make educated guesses based on the content.
Return only a JSON object with these three fields.

XML Content:
{content_for_llm}

Expected format:
{{
    "title": "feed title",
    "description": "feed description",
    "link": "main website link"
}}"""

    try:
        response = openai_client.ChatCompletion.create(
            model="4o-mini", messages=[{"role": "user", "content": prompt}]
        )

        result = json.loads(response["choices"][0]["message"]["content"])
        return {
            "title": result.get("title", ""),
            "description": result.get("description", ""),
            "link": result.get("link", ""),
        }
    except Exception as e:
        print(f"LLM extraction error: {e}")
        return {"title": "", "description": "", "link": ""}
