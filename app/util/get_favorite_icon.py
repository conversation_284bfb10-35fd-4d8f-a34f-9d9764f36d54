#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse

def get_favicon_url(url):
    # 模拟 Chrome 浏览器请求头
    headers = {
        "User-Agent": (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/115.0.5790.171 Safari/537.36"
        )
    }
    try:
        response = requests.get(url, headers=headers, timeout=10)
        if not response.ok:
            print("无法获取网页内容，状态码:", response.status_code)
            return None
    except Exception as err:
        print("请求过程中发生异常:", err)
        return None

    # 解析 HTML
    soup = BeautifulSoup(response.text, "html.parser")

    # 尝试查找包含 favicon 的 link 标签
    # 常见的 rel 值可能有 "icon", "shortcut icon", "apple-touch-icon"
    icon_link = None
    icon_rels = ["icon", "shortcut icon", "apple-touch-icon"]
    for rel in icon_rels:
        # 查找时忽略大小写，可以借助 lambda 函数进行判断
        tag = soup.find("link", rel=lambda value: value and rel in value.lower())
        if tag and tag.get("href"):
            icon_link = tag.get("href")
            break

    # 如果找到 favicon 链接，则转换成绝对地址
    if icon_link:
        favicon_url = urljoin(url, icon_link)
    else:
        # 未找到时默认使用网站根目录下的 /favicon.ico
        parsed_url = urlparse(url)
        favicon_url = f"{parsed_url.scheme}://{parsed_url.netloc}/favicon.ico"

    return favicon_url