from app.util.dify_client.dify import CompletionClient


def test_completion_client():
    complition_client = CompletionClient(
        base_url="http://100.98.45.17/v1",
        api_key="app-GX5nHxUzRamyE7FpO6SNllIW",
    )

    input = {
        "query": "What is the capital of France?",
    }

    completion_response = complition_client.create_completion_message(
        inputs=input, response_mode="blocking", user="user_1"
    )
    print(str(completion_response.content))
