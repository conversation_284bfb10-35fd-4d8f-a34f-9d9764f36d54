from enum import Enum
from typing import Dict, List, Union, Any


class ResponseMode(str, Enum):
    STREAMING = "streaming"
    BLOCKING = "blocking"


class FileType(str, Enum):
    IMAGE = "image"


class TransferMethod(str, Enum):
    REMOTE_URL = "remote_url"
    LOCAL_FILE = "local_file"


class NodeStatus(str, Enum):
    RUNNING = "running"
    SUCCEEDED = "succeeded"
    FAILED = "failed"
    STOPPED = "stopped"


class WorkflowStatus(str, Enum):
    RUNNING = "running"
    SUCCEEDED = "succeeded"
    FAILED = "failed"
    STOPPED = "stopped"


class Event(str, Enum):
    WORKFLOW_STARTED = "workflow_started"
    NODE_STARTED = "node_started"
    NODE_FINISHED = "node_finished"
    WORKFLOW_FINISHED = "workflow_finished"
    TTS_MESSAGE = "tts_message"
    TTS_MESSAGE_END = "tts_message_end"
    PING = "ping"


class UserModel:
    def __init__(self, user_id: str):
        self.user_id = user_id


class FileModel:
    def __init__(
        self,
        type: FileType,
        transfer_method: TransferMethod,
        upload_file_id: str = None,
        url: str = None,
    ):
        self.type = type
        self.transfer_method = transfer_method
        self.url = url
        self.upload_file_id = upload_file_id


class TextInputModel:
    def __init__(self, label: str, variable: str, required: bool, default: str):
        self.label = label
        self.variable = variable
        self.required = required
        self.default = default


class ParagraphModel(TextInputModel):
    pass


class SelectModel:
    def __init__(
        self,
        label: str,
        variable: str,
        required: bool,
        default: str,
        options: List[str],
    ):
        self.label = label
        self.variable = variable
        self.required = required
        self.default = default
        self.options = options


class UserInputFormModel:
    text_inputs: List[TextInputModel]
    paragraphs: List[ParagraphModel]
    selects: List[SelectModel]

    def __init__(
        self,
        text_inputs: List[TextInputModel],
        paragraphs: List[ParagraphModel],
        selects: List[SelectModel],
    ):
        self.text_inputs = text_inputs
        self.paragraphs = paragraphs
        self.selects = selects


class ImageModel:
    def __init__(
        self, enabled: bool, number_limits: int, transfer_methods: List[TransferMethod]
    ):
        self.enabled = enabled
        self.number_limits = number_limits
        self.transfer_methods = transfer_methods


class SystemParametersModel:
    def __init__(self, image_file_size_limit: str):
        self.image_file_size_limit = image_file_size_limit


# maybe depreacated
# class ChunkCompletionResponseModel:
#     def __init__(self, event: Event, task_id: str, workflow_run_id: str, data: Dict):
#         self.event = event
#         self.task_id = task_id
#         self.workflow_run_id = workflow_run_id
#         self.data = data


class NodeStartedModel:
    def __init__(
        self,
        id: str,
        node_id: str,
        node_type: str,
        title: str,
        index: int,
        predecessor_node_id: str,
        inputs: List,
        created_at: int,
    ):
        self.id = id
        self.node_id = node_id
        self.node_type = node_type
        self.title = title
        self.index = index
        self.predecessor_node_id = predecessor_node_id
        self.inputs = inputs
        self.created_at = created_at


class NodeFinishedModel:
    def __init__(
        self,
        id: str,
        node_id: str,
        node_type: str,
        title: str,
        index: int,
        predecessor_node_id: str,
        inputs: List,
        process_data: Dict,
        outputs: Dict,
        status: NodeStatus,
        error: str,
        elapsed_time: float,
        execution_metadata: Dict,
        total_tokens: int,
        total_price: float,
        currency: str,
        created_at: int,
    ):
        self.id = id
        self.node_id = node_id
        self.node_type = node_type
        self.title = title
        self.index = index
        self.predecessor_node_id = predecessor_node_id
        self.inputs = inputs
        self.process_data = process_data
        self.outputs = outputs
        self.status = status
        self.error = error
        self.elapsed_time = elapsed_time
        self.execution_metadata = execution_metadata
        self.total_tokens = total_tokens
        self.total_price = total_price
        self.currency = currency
        self.created_at = created_at


class WorkflowStartedModel:
    def __init__(
        self, id: str, workflow_id: str, sequence_number: int, created_at: int
    ):
        self.id = id
        self.workflow_id = workflow_id
        self.sequence_number = sequence_number
        self.created_at = created_at


class WorkflowFinishedModel:
    def __init__(
        self,
        id: str,
        workflow_id: str,
        status: WorkflowStatus,
        outputs: Dict,
        error: str,
        elapsed_time: float,
        total_tokens: int,
        total_steps: int,
        created_at: int,
        finished_at: int,
    ):
        self.id = id
        self.workflow_id = workflow_id
        self.status = status
        self.outputs = outputs
        self.error = error
        self.elapsed_time = elapsed_time
        self.total_tokens = total_tokens
        self.total_steps = total_steps
        self.created_at = created_at
        self.finished_at = finished_at


class TTSMessagesModel:
    def __init__(self, task_id: str, message_id: str, audio: str, created_at: int):
        self.task_id = task_id
        self.message_id = message_id
        self.audio = audio
        self.created_at = created_at

class ChunkCompletionResponseModel:
    def __init__(self, response: Dict[str, Any]):
        self.event: str = response.get("event", "")
        self.task_id: str = response.get("task_id", "")
        self.id: str = response.get("id", "")
        self.message_id: str = response.get("message_id", "")
        self.mode: str = response.get("mode", "")
        self.answer: str = response.get("answer", "")
        self.metadata: Dict[str, Any] = response.get("metadata", {})
        self.created_at: int = response.get("created_at", 0)

    @classmethod
    def from_dict(cls, response: Dict[str, Any]) -> "ChunkCompletionResponseModel":
        return cls(response)

    def __repr__(self):
        return (
            f"ChunkCompletionResponseModel(event={self.event}, task_id={self.task_id}, "
            f"id={self.id}, message_id={self.message_id}, mode={self.mode}, "
            f"answer={self.answer}, metadata={self.metadata}, created_at={self.created_at})"
        )

class CompletionResponseModel:
    def __init__(
        self,
        workflow_run_id: str,
        task_id: str,
        data: WorkflowFinishedModel,
    ):
        self.workflow_run_id = workflow_run_id
        self.task_id = task_id
        self.data = data


class ExecutionStatusModel:
    def __init__(
        self,
        id: str,
        workflow_id: str,
        status: WorkflowStatus,
        inputs: Dict,
        outputs: Dict,
        error: str,
        total_steps: int,
        total_tokens: int,
        created_at: int,
        finished_at: int,
        elapsed_time: float,
    ):
        self.id = id
        self.workflow_id = workflow_id
        self.status = status
        self.inputs = inputs
        self.outputs = outputs
        self.error = error
        self.total_steps = total_steps
        self.total_tokens = total_tokens
        self.created_at = created_at
        self.finished_at = finished_at
        self.elapsed_time = elapsed_time
