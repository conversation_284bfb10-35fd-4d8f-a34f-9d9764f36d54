from typing import Dict
import requests
from app.logging import get_logger

from app.util.dify_client.model import (
    ChunkCompletionResponseModel,
    ExecutionStatusModel,
    ResponseMode,
    UserModel,
)

logger = get_logger()

class DifyClient:
    def __init__(self, api_key, base_url):
        self.api_key = api_key
        self.base_url = base_url

    def _send_request(
        self, method, endpoint, json=None, params=None, stream=False
    ) -> requests.Response:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        url = f"{self.base_url}{endpoint}"
        logger.debug(f"Sending request to {url}")
        response = requests.request(
            method, url, json=json, params=params, headers=headers, stream=stream
        )

        return response

    def _send_request_with_files(
        self, method, endpoint, data, files
    ) -> requests.Response:
        headers = {"Authorization": f"Bearer {self.api_key}"}

        url = f"{self.base_url}{endpoint}"
        response = requests.request(
            method, url, data=data, headers=headers, files=files
        )

        return response

    def message_feedback(self, message_id, rating, user) -> requests.Response:
        data = {"rating": rating, "user": user}
        return self._send_request("POST", f"/messages/{message_id}/feedbacks", data)

    def get_application_parameters(self, user) -> requests.Response:
        params = {"user": user}
        return self._send_request("GET", "/parameters", params=params)

    def file_upload(self, user, files) -> requests.Response:
        data = {"user": user}
        return self._send_request_with_files(
            "POST", "/files/upload", data=data, files=files
        )


class WorkflowClient(DifyClient):
    def __init__(self, api_key, base_url):
        super().__init__(api_key, base_url)

    def run(
        self, user: UserModel, response_mode: ResponseMode, inputs: Dict, files=None
    ) -> ChunkCompletionResponseModel:
        if response_mode not in ["blocking", "streaming"]:
            raise ValueError("response_mode must be 'blocking' or 'streaming'")
        data = {
            "inputs": inputs,
            "response_mode": response_mode.value,
            "user": user.user_id,
            "files": files,
        }
        response = self._send_request(
            "POST",
            "/workflows/run",
            data,
            stream=True if response_mode == "streaming" else False,
        )
        logger.debug(f"Workflow response: {response}")
        if response.status_code == 200:
            return ChunkCompletionResponseModel(**response.json())
        else:
            raise Exception(f"Error running workflow: {response.text}")

    def get_workflow(self, workflow_id: str) -> ExecutionStatusModel:
        response = self._send_request("GET", f"/workflows/{workflow_id}")
        if response.status_code == 200:
            return ExecutionStatusModel(**response.json())
        else:
            raise Exception(f"Error getting execution status: {response.text}")

    def stop_workflow(self, user: UserModel, task_id: str):
        data = {"user": user.user_id}
        response = self._send_request("POST", f"/workflows/tasks/{task_id}/stop", data)
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Error stopping task: {response.text}")


class CompletionClient(DifyClient):
    def create_completion_message(
        self, inputs: object, response_mode: ResponseMode, user: UserModel, files=None
    ) -> ChunkCompletionResponseModel:
        if response_mode not in ["blocking", "streaming"]:
            raise ValueError("response_mode must be 'blocking' or 'streaming'")
        data = {
            "inputs": inputs,
            "response_mode": response_mode.value,
            "user": user.user_id,
            "files": files,
        }
        response = self._send_request(
            method="POST",
            endpoint="/completion-messages",
            json=data,
            params=None,
            stream=True if response_mode.__str__() == "streaming" else False,
        )
        logger.debug(f"Completion response: {response}")
        if response.status_code == 200:
            logger.debug(f"Completion response: {response.json()}")
            return ChunkCompletionResponseModel.from_dict(response.json())
        else:
            raise Exception(f"Error create completion: {response.text}")


class ChatClient(DifyClient):
    def create_chat_message(
        self,
        inputs,
        query,
        user,
        response_mode="blocking",
        conversation_id=None,
        files=None,
    ):
        data = {
            "inputs": inputs,
            "query": query,
            "user": user,
            "response_mode": response_mode,
            "files": files,
        }
        if conversation_id:
            data["conversation_id"] = conversation_id

        return self._send_request(
            "POST",
            "/chat-messages",
            data,
            stream=True if response_mode == "streaming" else False,
        )

    def get_conversation_messages(
        self, user, conversation_id=None, first_id=None, limit=None
    ):
        params = {"user": user}

        if conversation_id:
            params["conversation_id"] = conversation_id
        if first_id:
            params["first_id"] = first_id
        if limit:
            params["limit"] = limit

        return self._send_request("GET", "/messages", params=params)

    def get_conversations(self, user, last_id=None, limit=None, pinned=None):
        params = {"user": user, "last_id": last_id, "limit": limit, "pinned": pinned}
        return self._send_request("GET", "/conversations", params=params)

    def rename_conversation(self, conversation_id, name, user):
        data = {"name": name, "user": user}
        return self._send_request(
            "POST", f"/conversations/{conversation_id}/name", data
        )
