import re
from urllib.parse import urlparse, urlunparse
from readability import Document

def shorten_url(url):
    parsed = urlparse(url)
    path = '/'.join(parsed.path.split('/')[:3])
    shortened = urlunparse((parsed.scheme, parsed.netloc, path, '', '', ''))
    return shortened

def process_html(html_content):
    doc = Document(html_content)
    summary = doc.summary()
    
    def replace_url(match):
        return f'<a href="{match.group(1)}">{shorten_url(match.group(1))}</a>'
    
    processed_html = re.sub(r'<a\s+(?:[^>]*?\s+)?href="([^"]*)"', replace_url, summary)
    return processed_html