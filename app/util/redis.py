import uuid
from redis import Redis
from app.logging import get_logger

# Initialize Redis client as a singleton
redis_client = Redis(host="localhost", port=6379, db=0)
logger = get_logger()


def get_redis_client() -> Redis:
    """
    Returns the shared Redis client instance.

    Returns:
        Redis: The shared Redis client instance
    """
    return redis_client


class RedisDistributedLock:
    """Redis分布式锁实现"""

    def __init__(self, redis: Redis, lock_key: str):
        """
        初始化Redis分布式锁

        Args:
            redis_client: Redis客户端实例
            lock_key: 锁的键名
        """
        self.redis = redis
        self.lock_key = lock_key
        self.identifier = str(uuid.uuid4())  # 用于标识当前锁的持有者
        self.acquired = False

    def __enter__(self):
        self.acquire()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.release()

    def acquire(self) -> bool:
        """
        尝试获取锁

        Returns:
            bool: 是否成功获取锁
        """
        # 使用SET NX命令尝试获取锁
        self.acquired = bool(self.redis.set(self.lock_key, self.identifier, nx=True))
        if self.acquired:
            logger.debug(f"Successfully acquired lock: {self.lock_key}")
        else:
            logger.debug(f"Failed to acquire lock: {self.lock_key}")
        return self.acquired

    def release(self) -> bool:
        """
        释放锁

        Returns:
            bool: 是否成功释放锁
        """
        if not self.acquired:
            return False

        # 使用Lua脚本确保原子性释放
        lua_script = """
        if redis.call('get', KEYS[1]) == ARGV[1] then
            return redis.call('del', KEYS[1])
        else
            return 0
        end
        """
        result = bool(self.redis.eval(lua_script, 1, self.lock_key, self.identifier))
        if result:
            logger.debug(f"Successfully released lock: {self.lock_key}")
            self.acquired = False
        else:
            logger.warning(f"Failed to release lock: {self.lock_key}")
        return result

    @classmethod
    def cleanup_locks(cls, redis: Redis, pattern: str = "crawler_lock:*"):
        """
        清理指定模式的锁

        Args:
            redis_client: Redis客户端实例，如果未提供则使用共享的Redis客户端
            pattern: 要清理的锁的键模式
        """
        client = redis
        keys = client.keys(pattern)
        if keys:
            redis_client.delete(*keys)
            logger.info(f"Cleaned up {len(keys)} locks matching pattern: {pattern}")
