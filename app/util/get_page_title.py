#!/usr/bin/env python3
import requests
from bs4 import BeautifulSoup

def get_page_title(url):
    # 如果没有 http:// 或 https:// 前缀，则默认加上 http://
    if not url.startswith("http://") and not url.startswith("https://"):
        url = "http://" + url

    # 模拟 Chrome 浏览器的 User-Agent
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }

    try:
        # 发送请求
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()  # 检查请求是否成功

        # 解析 HTML 内容
        soup = BeautifulSoup(response.text, "html.parser")
        title_tag = soup.find("title")
        if title_tag:
            return title_tag.get_text(strip=True)
        else:
            return "没有找到网页标题"
    except requests.exceptions.RequestException as e:
        return f"获取网页信息失败，错误信息：{e}"

if __name__ == "__main__":
    user_input = input("请输入网址：")
    title = get_page_title(user_input)
    print("网页标题为：", title)