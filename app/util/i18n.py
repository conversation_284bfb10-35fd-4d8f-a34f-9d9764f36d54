from typing import Optional, Dict, Any
from fastapi import Depends, Request, Header
from enum import Enum
import json
import os
from pathlib import Path
from app.logging import get_logger

logger = get_logger()

# 定义支持的语言
class Locale(str, Enum):
    EN = "en"
    ZH = "zh"

# 翻译文件目录
TRANSLATIONS_DIR = Path(__file__).parent.parent / "translations"

# 确保翻译目录存在
os.makedirs(TRANSLATIONS_DIR, exist_ok=True)

# 加载翻译文件
_translations: Dict[str, Dict[str, str]] = {}

def _load_translations():
    """加载所有翻译文件"""
    for locale in Locale:
        translation_file = TRANSLATIONS_DIR / f"{locale.value}.json"
        if translation_file.exists():
            try:
                with open(translation_file, "r", encoding="utf-8") as f:
                    _translations[locale.value] = json.load(f)
                logger.info(f"Loaded translations for {locale.value}")
            except Exception as e:
                logger.error(f"Failed to load translations for {locale.value}: {e}")
                _translations[locale.value] = {}
        else:
            # 创建空的翻译文件
            with open(translation_file, "w", encoding="utf-8") as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
            _translations[locale.value] = {}
            logger.info(f"Created empty translation file for {locale.value}")

# 初始加载翻译
_load_translations()

def get_translation(key: str, locale: Locale = Locale.EN) -> str:
    """获取指定键的翻译，如果不存在则返回键本身"""
    translations = _translations.get(locale, {})
    return translations.get(key, key)

async def get_locale(
    accept_language: Optional[str] = Header(None, alias="Accept-Language")
) -> Locale:
    """
    从请求头中获取语言设置，默认为英语
    
    可以从 Accept-Language 头部获取，例如：
    - en, en-US: 英语
    - zh, zh-CN, zh-TW: 中文
    """
    if not accept_language:
        return Locale.EN
    
    # 简单解析 Accept-Language 头
    # 例如: "zh-CN,zh;q=0.9,en;q=0.8"
    languages = accept_language.split(',')
    primary_lang = languages[0].split(';')[0].strip().lower()
    
    if primary_lang.startswith('zh'):
        return Locale.ZH
    
    # 默认返回英语
    return Locale.EN

class I18nDependency:
    """国际化依赖类，可以注入到 FastAPI 路由中"""
    
    def __init__(self, locale: Locale = Depends(get_locale)):
        self.locale = locale
    
    def translate(self, key: str) -> str:
        """翻译指定的键"""
        return get_translation(key, self.locale)
    
    @property
    def current_locale(self) -> str:
        """获取当前语言代码"""
        return self.locale.value 