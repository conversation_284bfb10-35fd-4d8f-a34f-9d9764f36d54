from bs4 import BeautifulSoup
import re
from app.db.session import Session

from app.service.shorten_url import restore_url as _restore_url, shorten_url

def restore_url(short_url,session:Session) -> str | None:
    """
    Function to send a GET request to our URL shortening service
    and return the original URL.
    """

    # Check if the short URL is valid
    if not short_url.startswith("http://su.o/"):
        print("Error: Invalid short URL format")
        return None

    short_code = short_url.split("/")[-1]
    return _restore_url(short_code,session)

def gen_short_url(long_url,session:Session)->str:
    return f"http://su.o/{shorten_url(long_url,session)}"

def process_html(html_content,session:Session)->str:
    """
    Function to process HTML content, find href links, and replace them with shortened versions.
    """
    soup = BeautifulSoup(html_content, "html.parser")

    # Find all 'a' tags with 'href' attribute
    for a_tag in soup.find_all("a", href=True):
        original_url = a_tag["href"]

        # Check if the URL is absolute (starts with http:// or https://)
        if re.match(r"https?://", original_url):
            shortened_url = gen_short_url(original_url,session)
            a_tag["href"] = shortened_url

    return str(soup)
