from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import and_, select

from app.db.session import get_db
from app.db.models import (
    SourceProvider,
    CompleteArticle,
    User,UserSource
)
from app.logging import get_logger
from app.schemas.api import (
    Article,
)
from app.api.user import get_user_id
from app.api.utils import CommonDependencies

router = APIRouter()
logger = get_logger()


@router.get("/", response_model=Article)
async def list_sources(
    article_id: int,
    source_id: int,
    db: Session = Depends(get_db),
    user_id: User = Depends(get_user_id),
    commons: CommonDependencies = Depends(CommonDependencies),
):
    has_priv = db.execute(
        select(UserSource.id)
        .join(
            SourceProvider, SourceProvider.id == UserSource.inner_source_id
        )
        .where(and_(UserSource.id == source_id, UserSource.user_id == user_id))
    ).first()
    if not has_priv:
        raise HTTPException(status_code=403, detail="Access denied")
    stmt = select(CompleteArticle).where(CompleteArticle.id == article_id)
    article = db.execute(stmt).first()
    if not article:
        raise HTTPException(status_code=404, detail="Article not found")
    return article[0] if article else None
