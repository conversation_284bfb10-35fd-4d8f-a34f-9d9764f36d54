from fastapi import Depends, HTTPException, status, APIRouter
from fastapi.security import <PERSON>A<PERSON>2Password<PERSON>earer, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from app.logging import get_logger
from app.db import engine, get_db
import app.db.models.user as models
import app.schemas as schemas
import app.util.auth as auth
from app.api.utils import CommonDependencies

logger = get_logger()

# Run the database migrations
models.Base.metadata.create_all(bind=engine)

# Initialize the FastAPI app
router = APIRouter()

# Define the OAuth2 scheme for token-based authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

@router.post("/register", response_model=schemas.User)
def register_user(
    user: schemas.UserCreate, 
    db: Session = Depends(get_db),
    commons: CommonDependencies = Depends(CommonDependencies)
):
    # Check if username already exists
    db_user = db.query(models.User).filter(models.User.username == user.username).first()
    if db_user:
        raise HTTPException(status_code=400, detail=commons.translate("user.already_registered"))
    
    # Check if email already exists
    db_email = db.query(models.User).filter(models.User.email == user.email).first()
    if db_email:
        raise HTTPException(status_code=400, detail=commons.translate("auth.email_already_registered"))
    
    # Create new user
    hashed_password = auth.get_password_hash(user.password)
    new_user = models.User(
        username=user.username, 
        email=user.email, 
        hashed_password=hashed_password,
    )
    db.add(new_user)
    db.commit()
    db.refresh(new_user)
    return new_user

async def get_user_id(
    token: str = Depends(oauth2_scheme),
)->int:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    token_data = auth.verify_token(token)
    if token_data is None:
        raise credentials_exception
    
    user_id = token_data.get("sub")
    if user_id is None:
        raise credentials_exception
    return int(user_id)
    
async def get_current_user(
    token: str = Depends(oauth2_scheme), 
    db: Session = Depends(get_db),
    commons: CommonDependencies = Depends(CommonDependencies)
):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail=commons.translate("auth.invalid_credentials"),
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    token_data = auth.verify_token(token)
    if token_data is None:
        raise credentials_exception
    
    user_id = token_data.get("sub")
    if user_id is None:
        raise credentials_exception

    user_id = int(user_id)
    
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if user is None:
        raise credentials_exception
   
    return user

@router.get("/me", response_model=schemas.User)
async def read_users_me(
    current_user: models.User = Depends(get_current_user),
    commons: CommonDependencies = Depends(CommonDependencies)
):
    logger.info(f"User {current_user.username} requested profile, locale: {commons.locale}")
    return current_user

@router.put("/me", response_model=schemas.User)
async def update_user(
    user_update: schemas.UserUpdate,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db),
    commons: CommonDependencies = Depends(CommonDependencies)
):
    # Update username if provided and not already taken
    if user_update.username and user_update.username != current_user.username:
        db_user = db.query(models.User).filter(models.User.username == user_update.username).first()
        if db_user:
            raise HTTPException(status_code=400, detail=commons.translate("user.username_taken"))
        current_user.username = user_update.username
    
    # Update email if provided and not already taken
    if user_update.email and user_update.email != current_user.email:
        db_email = db.query(models.User).filter(models.User.email == user_update.email).first()
        if db_email:
            raise HTTPException(status_code=400, detail=commons.translate("auth.email_already_registered"))
        current_user.email = user_update.email
    
    # Update password if provided
    if user_update.password:
        current_user.hashed_password = auth.get_password_hash(user_update.password)
    
    db.commit()
    db.refresh(current_user)
    return current_user
