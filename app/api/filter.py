from typing import List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import select
from sqlalchemy.orm import Session

from app.api.user import get_user_id
from app.db.models import (
    Filter,
    FilterCharacterSetting,
    SourceProvider,
    UserSource,
)
from app.db.models.user import User
from app.db.session import get_db
from app.logging import get_logger
from app.schemas.api import (
    CreateFilterCharacterSettingSchema,
    FilterInfo,
)
from app.api.utils import CommonDependencies

router = APIRouter()
logger = get_logger()

@router.get("/", response_model=List[FilterInfo])
async def list_filter(
    db: Session = Depends(get_db),
    user_id: User = Depends(get_user_id),
    commons: CommonDependencies = Depends(CommonDependencies)
):
    stmt = select(FilterCharacterSetting).where(
        FilterCharacterSetting.user_id == user_id
    )
    result = db.execute(stmt).scalars().all()
    return [FilterInfo(id=x.id, name=x.name) for x in result]

@router.post("/")
async def add_filter(
    fc: CreateFilterCharacterSettingSchema | None = None,
    db: Session = Depends(get_db),
    user_id: User = Depends(get_user_id),
    commons: CommonDependencies = Depends(CommonDependencies)
):
    if fc is None:
        raise HTTPException(
            status_code=400, detail=commons.translate("filter.required")
        )
    stmt = select(FilterCharacterSetting).where(
        FilterCharacterSetting.user_id == user_id.id,
        FilterCharacterSetting.name == fc.name,
    )
    if db.execute(stmt).scalar() is not None:
        raise HTTPException(status_code=400, detail=commons.translate("filter.already_exists"))

    filter_ids = []
    for f in fc.filters:
        filter = Filter(**f.model_dump())
        db.add(filter)
        db.flush()
        filter_ids.append(filter.id)
    
    # convert user source ids to inner source ids
    stmt = select(SourceProvider.id).join(UserSource, SourceProvider.id == UserSource.inner_source_id).where(
        UserSource.user_id == user_id.id,
        UserSource.id.in_(fc.source_ids),
    )
    inner_source_ids = [x.id for x in db.execute(stmt).scalars().all()]

    filter_character_setting = FilterCharacterSetting(
        source_ids=inner_source_ids,
        filters=filter_ids,
        name=fc.name,
        user_id=user_id.id,
    )
    db.add(filter_character_setting)
    db.commit()
    return

@router.delete("/")
async def delete_filter(
    id: int,
    db: Session = Depends(get_db),
    user_id: User = Depends(get_user_id),
    commons: CommonDependencies = Depends(CommonDependencies)
):
    stmt = select(FilterCharacterSetting).where(
        FilterCharacterSetting.id == id,
        FilterCharacterSetting.user_id == user_id.id,
    )
    filter_character_setting = db.execute(stmt).scalar()
    if filter_character_setting is None:
        raise HTTPException(status_code=404, detail=commons.translate("filter.not_found"))
    db.delete(filter_character_setting)
    db.commit()
    return

@router.put("/")
async def update_filter(
    id: int,
    fc: CreateFilterCharacterSettingSchema,
    db: Session = Depends(get_db),
    user_id: User = Depends(get_user_id),
    commons: CommonDependencies = Depends(CommonDependencies)
):
    stmt = select(FilterCharacterSetting).where(
        FilterCharacterSetting.id == id,
        FilterCharacterSetting.user_id == user_id.id,
    )
    filter_character_setting = db.execute(stmt).scalar()
    if filter_character_setting is None:
        raise HTTPException(status_code=404, detail=commons.translate("filter.not_found"))

    # delete old filters
    for f in filter_character_setting.filters:
        db.delete(f)
    db.flush()

    # add new filters
    filter_ids = []
    for f in fc.filters:
        filter = Filter(**f.model_dump())
        db.add(filter)
        db.flush()
        filter_ids.append(filter.id)
    
    # validate source_ids and convert to inner source ids
    stmt = select(SourceProvider.id).join(UserSource, SourceProvider.id == UserSource.inner_source_id).where(
        UserSource.user_id == user_id.id,
        UserSource.id.in_(fc.source_ids),
    )
    inner_source_ids = [x.id for x in db.execute(stmt).scalars().all()]
    if len(inner_source_ids) != len(fc.source_ids):
        raise HTTPException(status_code=400, detail=commons.translate("filter.invalid_source_ids"))
    
    filter_character_setting.source_ids = inner_source_ids
    filter_character_setting.filters = filter_ids
    filter_character_setting.name = fc.name
    db.commit()
    return