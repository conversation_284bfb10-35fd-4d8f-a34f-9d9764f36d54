from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import select
from sqlalchemy.orm import Session

from app.api.utils import get_reference_articles
from app.api.user import get_user_id
from app.db.models import (
    TopicContent,
)
from app.db.models.user import User
from app.db.session import get_db
from app.logging import get_logger
from app.schemas.api import (
    TopicSummary,
)
from typing import List

from sqlalchemy import and_

from app.db.models import (
    FilterCharacterSetting,
)
from app.api.utils import CommonDependencies

router = APIRouter()
logger = get_logger()

@router.get("/", response_model=List[TopicSummary])
async def list_filter_articles(
    db: Session = Depends(get_db),
    filter_id: int = -1,
    limit: int = 10,
    cursor: int = -1,
    direction: str = "next",
    wanted: bool = True,
    user_id: User = Depends(get_user_id),
    commons: CommonDependencies = Depends(CommonDependencies)
):
    logger.info(f"LXDEBUG filter_id: {filter_id}")
    if filter_id < 0:
        raise HTTPException(status_code=400, detail=commons.translate("topic.filter_id_required"))

    if direction not in ["next", "prev"]:
        raise HTTPException(
            status_code=400, detail=commons.translate("topic.invalid_direction")
        )
    
    # make sure filter_id owned by user
    stmt = select(FilterCharacterSetting).where(
        FilterCharacterSetting.user_id == user_id,
        FilterCharacterSetting.id == filter_id,
    )
    if db.execute(stmt).scalar() is None:
        raise HTTPException(status_code=404, detail=commons.translate("filter.not_found"))

    # build base query
    base_query = select(TopicContent).where(
        and_(TopicContent.filter_character_id == filter_id, TopicContent.wanted == wanted)
    )

    # apply different query conditions based on cursor and direction
    if cursor < 0:
        # return latest content when no cursor
        stmt = base_query.order_by(TopicContent.id.desc()).limit(limit)
    else:
        if direction == "next":
            # load next content
            stmt = (
                base_query.where(TopicContent.id < cursor)
                .order_by(TopicContent.id.desc())
                .limit(limit)
            )
        else:
            # load prev content
            stmt = (
                base_query.where(TopicContent.id > cursor)
                .order_by(TopicContent.id.asc())
                .limit(limit)
            )

    topics = db.execute(stmt).scalars().all()

    # if load prev content, reverse the result to keep consistent order
    if cursor is not None and direction == "prev":
        topics = list(reversed(topics))

    locale = commons.locale
    return [
        TopicSummary(
            id=topic.id,
            title=topic.title,
            summary=topic.summary_zh if locale == 'zh' else topic.summary,
            ts=topic.ts,
            image_url=topic.image_url,
            reference_articles=await get_reference_articles(db, topic_id=topic.id),
        )
        for topic in topics
    ]


@router.get("/{id}", response_model=TopicSummary)
async def get_summary(
    id: int,
    db: Session = Depends(get_db),
    user_id: User = Depends(get_user_id),
    commons: CommonDependencies = Depends(CommonDependencies)
):
    summary = db.query(TopicContent).filter(TopicContent.id == id).first()
    if summary is None:
        raise HTTPException(status_code=404, detail=commons.translate("topic.not_found"))
    
    # check if filter_character_id is owned by user
    stmt = select(FilterCharacterSetting).where(
        FilterCharacterSetting.user_id == user_id,
        FilterCharacterSetting.id == summary.filter_character_id,
    )
    if db.execute(stmt).scalar() is None:
        raise HTTPException(status_code=404, detail=commons.translate("topic.not_found"))

    locale = commons.locale
    return TopicSummary(
        id=summary.id,
        title=summary.title,
        summary=summary.summary_zh if locale == 'zh' else summary.summary,
        ts=summary.ts,
        image_url=summary.image_url,
        reference_articles=await get_reference_articles(db, topic_id=summary.id),
    )
