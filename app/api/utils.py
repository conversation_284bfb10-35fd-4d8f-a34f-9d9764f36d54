from app.db.models  import ArticleTopicRelation, CompleteArticle,UserSource
from app.schemas.api import ReferenceArticles
from sqlalchemy import select
from sqlalchemy.orm import Session
from typing import List
from fastapi import Depends
from app.util.i18n import I18nDependency


async def get_reference_articles(db: Session, topic_id: int) -> List[ReferenceArticles]:
    stmt = (
        (
            select(
                ArticleTopicRelation.article_id,
                UserSource.id,
                CompleteArticle.title,
                CompleteArticle.origin_url,
            )
        )
        .join(CompleteArticle, ArticleTopicRelation.article_id == CompleteArticle.id)
        .join(UserSource, CompleteArticle.source_id == UserSource.inner_source_id)
        .where(ArticleTopicRelation.topic_id == topic_id)
    )
    result = db.execute(stmt).all()
    return [ReferenceArticles(id=r[0], user_source_id=r[1], title=r[2], link=r[3]) for r in result]


class CommonDependencies:
    """
    通用依赖项父类，包含所有API路由共享的依赖项
    可以被用作其他依赖项的父类
    """
    
    def __init__(self, i18n: I18nDependency = Depends(I18nDependency)):
        """
        初始化通用依赖项
        
        Args:
            i18n: 国际化依赖项，提供翻译功能
        """
        self.i18n = i18n
    
    def translate(self, key: str) -> str:
        """
        翻译指定的键
        
        Args:
            key: 翻译键
            
        Returns:
            翻译后的文本
        """
        return self.i18n.translate(key)
    
    @property
    def locale(self) -> str:
        """
        获取当前语言代码
        
        Returns:
            当前语言代码，例如 'en' 或 'zh'
        """
        return self.i18n.current_locale

