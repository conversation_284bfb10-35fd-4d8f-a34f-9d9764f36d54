from fastapi import APIRouter
from . import filter, source, user, topic, article

api_router = APIRouter()

api_router.include_router(user.router, prefix="/users", tags=["users"])
api_router.include_router(source.router, prefix="/source", tags=["source"])
api_router.include_router(filter.router, prefix="/filter", tags=["filter"])
api_router.include_router(topic.router, prefix="/topic", tags=["topic"])
api_router.include_router(article.router, prefix="/article", tags=["article"])
