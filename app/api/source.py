from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import and_,select
from typing import List, Union

from datetime import datetime, timezone
from app.db.session import get_db
from app.db.models import (
    SourceProvider,
    SourceProviderType,
    SourceSubProviderType,
)
from app.logging import get_logger
from app.schemas.api import (
    AddSourceResponse,
    ParseSourceResponse,
    SourceResponse,
    ParseSourceRequest,
    AddSourceRequest,
    DeleteProviderRequest,
    HackerNewsSourceResponse,
    EmailSourceResponse,
    RssSourceResponse,
)
from app.service.scraper.website.general_website import (
    get_website_metadata,
)
from app.service.scraper.email.list_sources import list_email_source
from app.service.scraper.website.hacker_news import (
    list_hn_source,
)
from app.service.scraper.rss.rss import (
    list_rss_source,
    get_rss_metadata,
)
from app.api.user import get_user_id
from app.db.models.user import User
from app.db.models.source import UserSource
from app.api.utils import CommonDependencies

router = APIRouter()
logger = get_logger()


@router.get("/", response_model=List[SourceResponse])
async def list_sources(
    db: Session = Depends(get_db), 
    user_id: User = Depends(get_user_id),
    commons: CommonDependencies = Depends(CommonDependencies)
):
    stmt = select(UserSource, SourceProvider.icon_url, SourceProvider.source_type, SourceProvider.source_sub_type).join(
        SourceProvider, UserSource.inner_source_id == SourceProvider.id
    ).where(UserSource.user_id == user_id)
    sources = db.execute(stmt).all()
    return [
        SourceResponse(
            id=source[0].id,
            source_type=source[2],
            source_sub_type=source[3],
            nickname=source[0].nickname,
            created_at=int(source[0].created_at.timestamp()) if source[0].created_at else 0,
            icon_url=source[1] or "",
            is_folder=source[0].is_folder,
            parent_id=source[0].parent_id,
            sort_order=source[0].sort_order,
        )
        for source in sources
    ]


# list pre-defined providers for each provider type
@router.get("/builtin", response_model=List[SourceResponse])
async def list_builtin_providers(
    source_type: SourceProviderType,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_user_id),
    commons: CommonDependencies = Depends(CommonDependencies)
):
    pass


@router.post("/parse", response_model=ParseSourceResponse)
async def parse_metadata(
    req: ParseSourceRequest, 
    current_user: User = Depends(get_user_id),
    commons: CommonDependencies = Depends(CommonDependencies)
) -> SourceProvider:
    icon_url = None

    # get icon_url and name if not provided
    match req.source_type:
        case SourceProviderType.email:
            if req.nickname is None:
                req.nickname = req.user
        case SourceProviderType.rss:
            assert req.url is not None  # 告诉类型检查器
            name, _, icon_url = await get_rss_metadata(req.url)
            if name and req.nickname == "":
                req.nickname = name
        case SourceProviderType.website:
            assert req.url is not None
            # 获取网站的元数据
            name, icon_url = await get_website_metadata(req.url)
            if name and req.nickname == "":
                req.nickname = name

    db_provider = SourceProvider(
        source_type=req.source_type,
        source_sub_type=req.source_sub_type,
        nickname=req.nickname,
        imap_server=req.imap_server,
        imap_port=req.imap_port,
        user=req.user,
        password=req.password,
        url=req.url,
        keywords=req.keywords,
        icon_url=icon_url,
    )
    return db_provider


@router.post("/add", response_model=AddSourceResponse)
async def add_source(
    req: AddSourceRequest,
    db: Session = Depends(get_db),
    user_id: User = Depends(get_user_id),
    commons: CommonDependencies = Depends(CommonDependencies)
):
    """add a new source provider

    Args:
        req (AddSourceRequest): user input params for the new provider
        db (Session, optional): db to store provider info. Defaults to Depends(get_db).
        user_id (User, optional): user id. Defaults to Depends(get_user_id).

    Raises:
        HTTPException: duplicate source

    Returns:
        AddSourceResponse: response after adding the source
    """
    # Check if user already has a source with this nickname
    stmt = select(UserSource).where(
        and_(UserSource.user_id == user_id, UserSource.nickname == req.nickname)
    )
    if db.execute(stmt).scalar() is not None:
        error_msg = commons.translate("source.already_exists").format(name=req.nickname)
        raise HTTPException(status_code=400, detail=error_msg)

    # Build query conditions based on source type
    provider_conditions = {
        SourceProviderType.email: and_(
            SourceProvider.user == req.user,
            SourceProvider.imap_server == req.imap_server,
            SourceProvider.imap_port == req.imap_port,
            SourceProvider.source_type == SourceProviderType.email,
            SourceProvider.password == req.password,
        ),
        SourceProviderType.rss: and_(
            SourceProvider.url == req.url,
            SourceProvider.source_type == SourceProviderType.rss,
        ),
        SourceProviderType.website: and_(
            SourceProvider.url == req.url,
            SourceProvider.source_type == SourceProviderType.website,
        ),
    }

    # Check if provider already exists
    provider = None
    if req.source_type in provider_conditions:
        stmt = select(SourceProvider).where(provider_conditions[req.source_type])
        provider = db.execute(stmt).scalar()

        # Check if user already has this provider
        if provider is not None:
            stmt = select(UserSource).where(
                and_(
                    UserSource.user_id == user_id,
                    UserSource.inner_source_id == provider.id,
                )
            )
            user_has_provider = db.execute(stmt).scalar() is not None

            if user_has_provider:
                error_msg = commons.translate("source.already_exists").format(name=req.nickname)
                raise HTTPException(status_code=400, detail=error_msg)

    if provider is None:
        # Create new shared provider
        provider = SourceProvider(
            source_type=req.source_type,
            source_sub_type=req.source_sub_type,
            nickname=req.nickname,
            imap_server=req.imap_server,
            imap_port=req.imap_port,
            user=req.user,
            password=req.password,
            url=req.url,
            keywords=req.keywords,
            icon_url=req.icon_url,
        )
        db.add(provider)
        db.flush()  # Get the ID without committing

    # Create user source
    user_source = UserSource(
        user_id=user_id,
        inner_source_id=provider.id,
        nickname=req.nickname,
        created_at=datetime.now(timezone.utc),
    )
    db.add(user_source)
    db.commit()
    db.refresh(user_source)

    return AddSourceResponse(id=user_source.id)


@router.delete("/")
def del_source(
    req: DeleteProviderRequest,
    db: Session = Depends(get_db),
    user_id: User = Depends(get_user_id),
    commons: CommonDependencies = Depends(CommonDependencies)
):
    stmt = select(UserSource).where(
        and_(
            UserSource.user_id == user_id,
            UserSource.id == req.id,
        )
    )
    source = db.execute(stmt).scalar()
    if not source:
        error_msg = commons.translate("source.not_found").format(id=req.id)
        raise HTTPException(status_code=404, detail=error_msg)
    db.delete(source)
    db.commit()
    return


@router.get(
    "/list",
    response_model=List[
        Union[HackerNewsSourceResponse, EmailSourceResponse, RssSourceResponse]
    ],
)
async def list_source_feeds(
    source_id: int,
    limit: int = 10,
    cursor: int | None = None,
    direction: str = "next",
    db: Session = Depends(get_db),
    user_id: User = Depends(get_user_id),
    commons: CommonDependencies = Depends(CommonDependencies)
):
    if direction not in ["next", "prev"]:
        raise HTTPException(
            status_code=400, detail=commons.translate("source.invalid_direction")
        )

    stmt = select(SourceProvider).join(
        UserSource,
        UserSource.inner_source_id == SourceProvider.id
    ).where(
        and_(UserSource.id == source_id, UserSource.user_id == user_id)
    )
    real_source = db.execute(stmt).scalar()
    if real_source is None:
        raise HTTPException(status_code=404, detail=f"Provider {source_id} not found")

    match real_source.source_type:
        case SourceProviderType.email:
            return await list_email_source(real_source.id, limit, cursor, direction, db)
        case SourceProviderType.website:
            if real_source.source_sub_type == SourceSubProviderType.hacker_news:
                return await list_hn_source(real_source.id, limit, cursor, direction, db)
        case SourceProviderType.rss:
            return await list_rss_source(real_source.id, limit, cursor, direction, db)
