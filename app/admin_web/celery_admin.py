from fastapi import APIRouter, Depends, HTTPException, Request, Form, Query
from fastapi.responses import HTMLResponse, RedirectResponse
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import List
import pickle

from app.db.session import get_db
from app.admin_web.admin_panel import get_current_admin_user
from app.admin_web.admin_panel import get_base_context, templates
from app.db import models
from app.logging import get_logger

logger = get_logger()
router = APIRouter()


def get_celery_app():
    """延迟导入celery_app以避免启动时的导入冲突"""
    try:
        from tasks.celery_app import celery_app
        return celery_app
    except Exception as e:
        logger.error(f"Failed to import celery_app: {e}")
        return None


def get_failed_tasks(db: Session, page: int = 1, page_size: int = 20):
    """获取失败的Celery任务"""
    offset = (page - 1) * page_size

    # 查询失败的任务
    query = text("""
        SELECT
            id,
            task_id,
            status,
            result,
            date_done,
            traceback,
            name,
            args,
            kwargs,
            worker,
            retries,
            queue
        FROM feeds.celery_taskmeta
        WHERE status = 'FAILURE'
        ORDER BY date_done DESC
        LIMIT :limit OFFSET :offset
    """)

    result = db.execute(query, {"limit": page_size, "offset": offset})
    tasks = result.fetchall()

    # 获取总数
    count_query = text("SELECT COUNT(*) FROM feeds.celery_taskmeta WHERE status = 'FAILURE'")
    total_count = db.execute(count_query).scalar()

    # 处理任务数据
    processed_tasks = []
    for task in tasks:
        try:
            # 解析args和kwargs
            args = []
            kwargs = {}
            if task.args:
                try:
                    args = pickle.loads(task.args)
                except:
                    args = []

            if task.kwargs:
                try:
                    kwargs = pickle.loads(task.kwargs)
                except:
                    kwargs = {}

            # 解析result (错误信息)
            error_message = ""
            error_type = ""
            if task.result:
                try:
                    result_data = pickle.loads(task.result)
                    if isinstance(result_data, dict):
                        if 'exc_message' in result_data:
                            error_message = str(result_data['exc_message'])
                        elif len(result_data) > 0:
                            error_message = str(result_data)
                        if 'exc_type' in result_data:
                            error_type = str(result_data['exc_type'])
                    else:
                        error_message = str(result_data)
                except:
                    error_message = "Failed to parse error message"

            processed_tasks.append({
                'id': task.id,
                'task_id': task.task_id,
                'status': task.status,
                'name': task.name or 'Unknown',
                'args': args,
                'kwargs': kwargs,
                'error_message': error_message,
                'error_type': error_type,
                'traceback': task.traceback,
                'date_done': task.date_done,
                'worker': task.worker,
                'retries': task.retries or 0,
                'queue': task.queue
            })
        except Exception as e:
            logger.error(f"Error processing task {task.task_id}: {e}")
            continue

    return processed_tasks, total_count


@router.get("/admin/celery-tasks", response_class=HTMLResponse)
async def celery_tasks_page(
    request: Request,
    page: int = Query(1, ge=1),
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Celery任务管理页面"""
    page_size = 20
    failed_tasks, total_count = get_failed_tasks(db, page, page_size)

    total_pages = ((total_count or 0) + page_size - 1) // page_size

    return templates.TemplateResponse(
        "admin/celery_tasks.html",
        get_base_context(
            request,
            current_user=current_admin,
            failed_tasks=failed_tasks,
            current_page=page,
            total_pages=total_pages,
            total_count=total_count,
            page_size=page_size
        )
    )


@router.post("/admin/celery-tasks/retry")
async def retry_tasks(
    request: Request,
    task_ids: List[str] = Form(...),
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """批量重试失败的任务"""
    if not task_ids:
        raise HTTPException(status_code=400, detail="No tasks selected")

    success_count = 0
    error_count = 0
    errors = []

    for task_id in task_ids:
        try:
            # 获取原始任务信息
            query = text("""
                SELECT name, args, kwargs
                FROM feeds.celery_taskmeta
                WHERE task_id = :task_id AND status = 'FAILURE'
            """)
            result = db.execute(query, {"task_id": task_id}).fetchone()

            if not result:
                errors.append(f"Task {task_id} not found or not failed")
                error_count += 1
                continue

            # 解析参数
            args = []
            kwargs = {}

            if result.args:
                try:
                    args = pickle.loads(result.args)
                except:
                    args = []

            if result.kwargs:
                try:
                    kwargs = pickle.loads(result.kwargs)
                except:
                    kwargs = {}

            # 重新提交任务
            task_name = result.name
            if task_name:
                # 获取任务函数
                celery_app = get_celery_app()
                if celery_app:
                    task_func = celery_app.tasks.get(task_name)
                    if task_func:
                        task_func.delay(*args, **kwargs)
                        success_count += 1
                        logger.info(f"Retried task {task_name} with ID {task_id}")
                    else:
                        errors.append(f"Task function {task_name} not found")
                        error_count += 1
                else:
                    errors.append(f"Celery app not available")
                    error_count += 1
            else:
                errors.append(f"Task {task_id} has no name - cannot retry without task name")
                error_count += 1

        except Exception as e:
            logger.error(f"Error retrying task {task_id}: {e}")
            errors.append(f"Error retrying task {task_id}: {str(e)}")
            error_count += 1

    # 返回结果页面或重定向
    if error_count == 0:
        message = f"Successfully retried {success_count} tasks"
    else:
        message = f"Retried {success_count} tasks, {error_count} errors occurred"

    # 重定向回任务列表页面，带上消息
    response = RedirectResponse(
        url=f"/admin/celery-tasks?message={message}",
        status_code=303
    )
    return response


@router.get("/admin/celery-tasks/{task_id}/detail", response_class=HTMLResponse)
async def task_detail(
    request: Request,
    task_id: str,
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """查看任务详情"""
    query = text("""
        SELECT
            id, task_id, status, result, date_done, traceback, name,
            args, kwargs, worker, retries, queue
        FROM feeds.celery_taskmeta
        WHERE task_id = :task_id
    """)

    result = db.execute(query, {"task_id": task_id}).fetchone()

    if not result:
        raise HTTPException(status_code=404, detail="Task not found")

    # 解析任务数据
    try:
        args = pickle.loads(result.args) if result.args else []
    except:
        args = []

    try:
        kwargs = pickle.loads(result.kwargs) if result.kwargs else {}
    except:
        kwargs = {}

    try:
        if result.result:
            error_data = pickle.loads(result.result)
            if isinstance(error_data, dict):
                error_message = error_data.get('exc_message', str(error_data))
                error_type = error_data.get('exc_type', 'Unknown')
            else:
                error_message = str(error_data)
                error_type = 'Unknown'
        else:
            error_message = ""
            error_type = ""
    except:
        error_message = "Failed to parse error data"
        error_type = "ParseError"

    task_detail = {
        'id': result.id,
        'task_id': result.task_id,
        'status': result.status,
        'name': result.name,
        'args': args,
        'kwargs': kwargs,
        'error_message': error_message,
        'error_type': error_type,
        'traceback': result.traceback,
        'date_done': result.date_done,
        'worker': result.worker,
        'retries': result.retries or 0,
        'queue': result.queue
    }

    return templates.TemplateResponse(
        "admin/celery_task_detail.html",
        get_base_context(
            request,
            current_user=current_admin,
            task=task_detail
        )
    )


@router.post("/admin/celery-tasks/cleanup")
async def cleanup_failed_tasks(
    request: Request,
    days_old: int = Form(7),
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """清理指定天数之前的失败任务"""
    try:
        # 删除指定天数之前的失败任务
        cleanup_query = text("""
            DELETE FROM feeds.celery_taskmeta
            WHERE status = 'FAILURE'
            AND date_done < NOW() - INTERVAL :days DAY
        """)

        # 先计算要删除的数量
        count_query = text("""
            SELECT COUNT(*) FROM feeds.celery_taskmeta
            WHERE status = 'FAILURE'
            AND date_done < NOW() - INTERVAL :days DAY
        """)
        deleted_count = db.execute(count_query, {"days": days_old}).scalar()

        # 执行删除
        db.execute(cleanup_query, {"days": days_old})
        db.commit()

        logger.info(f"Cleaned up {deleted_count} failed tasks older than {days_old} days")

        message = f"Successfully cleaned up {deleted_count} failed tasks older than {days_old} days"

    except Exception as e:
        logger.error(f"Error cleaning up failed tasks: {e}")
        message = f"Error cleaning up failed tasks: {str(e)}"

    # 重定向回任务列表页面
    response = RedirectResponse(
        url=f"/admin/celery-tasks?message={message}",
        status_code=303
    )
    return response
