from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, Request, Form, status, <PERSON><PERSON>, Response
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>RequestForm, OAuth2Password<PERSON>ear<PERSON>, HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import List, Optional
import app.db.models.user as models
import app.schemas as schemas
from app.db import get_db
from app.util.auth import verify_password, create_access_token
from app.logging import get_logger
from datetime import timedelta, datetime
from pathlib import Path

# Initialize the templates
templates = Jinja2Templates(directory=str(Path(__file__).parent.parent / "templates"))

# Initialize the FastAPI router
router = APIRouter()

# Use HTTPBearer for more flexible token extraction
security = HTTPBearer(auto_error=False)

# Set up logger
logger = get_logger()

# Helper function to add common context variables to all templates
def get_base_context(request: Request, current_user=None, **kwargs):
    """Add base context variables to all templates"""
    context = {
        "request": request,
        "current_user": current_user,
        "current_year": datetime.now().year,
        **kwargs
    }
    return context

# Authentication functions
async def get_current_admin_user(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
):
    """
    Dependency to get the current admin user from the token.
    Verifies that the user exists and has admin privileges by checking
    if they are in the AdminUser table.
    Supports both bearer token and cookie authentication.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    permission_exception = HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail="Not enough permissions",
        headers={"WWW-Authenticate": "Bearer"},
    )

    # Get token from Authorization header if present
    token = None
    if credentials:
        token = credentials.credentials

    # If no token in Authorization header, try to get from cookie
    if token is None:
        if "access_token" in request.cookies:
            cookie_token = request.cookies.get("access_token")

            # Extract the token from the cookie (remove "Bearer " prefix if present)
            if cookie_token.startswith("Bearer "):
                token = cookie_token[7:]
            else:
                token = cookie_token

    # If still no token, raise exception
    if token is None:
        raise credentials_exception

    # Verify token and get user ID
    from app.util.auth import verify_token
    token_data = verify_token(token)
    if token_data is None:
        raise credentials_exception


    user_id = token_data.get("sub")
    if user_id is None:
        raise credentials_exception

    user_id = int(user_id)

    # Get user from database
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if user is None:
        raise credentials_exception


    # Check if user has admin privileges by looking up in AdminUser table
    admin_user = db.query(models.AdminUser).filter(models.AdminUser.user_id == user.id).first()
    if admin_user is None:
        raise permission_exception

    return user

# Login routes
@router.get("/admin/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """
    Display the admin login page
    """
    return templates.TemplateResponse(
        "admin/login.html",
        get_base_context(request)
    )

@router.post("/admin/login", response_class=HTMLResponse)
async def login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db)
):
    """
    Handle admin login form submission
    """
    # Check if user exists and credentials are valid
    user = db.query(models.User).filter(models.User.email == username).first()

    if not user or not verify_password(password, user.hashed_password):
        return templates.TemplateResponse(
            "admin/login.html",
            get_base_context(
                request,
                error="Invalid email or password"
            ),
            status_code=401
        )

    # Check if user is an admin
    admin_user = db.query(models.AdminUser).filter(models.AdminUser.user_id == user.id).first()
    if not admin_user:
        return templates.TemplateResponse(
            "admin/login.html",
            get_base_context(
                request,
                error="You do not have admin privileges"
            ),
            status_code=403
        )

    # Create access token
    access_token_expires = timedelta(minutes=60 * 24)  # Longer session for admin - 24 hours
    access_token = create_access_token(
        data={"sub": str(user.id)},
        expires_delta=access_token_expires
    )

    # Redirect to admin dashboard with token
    response = RedirectResponse(
        url="/admin",
        status_code=status.HTTP_303_SEE_OTHER
    )

    # Set the cookie for authentication
    response.set_cookie(
        key="access_token",
        value=f"Bearer {access_token}",
        httponly=True,
        max_age=3600 * 24,  # 24 hours
        secure=False,       # Set to True in production with HTTPS
        samesite="lax"
    )

    return response

@router.get("/admin/logout", response_class=HTMLResponse)
async def logout(request: Request):
    """
    Log out the admin user by clearing the token cookie
    """
    response = RedirectResponse(
        url="/admin/login",
        status_code=status.HTTP_303_SEE_OTHER
    )
    response.delete_cookie(key="access_token")

    return response

# Admin dashboard
@router.get("/admin", response_class=HTMLResponse)
async def admin_dashboard(
    request: Request,
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Admin dashboard showing overview of users and admins
    """
    users_count = db.query(models.User).count()
    admins_count = db.query(models.AdminUser).count()

    # Get failed tasks count
    try:
        failed_tasks_query = text("SELECT COUNT(*) FROM feeds.celery_taskmeta WHERE status = 'FAILURE'")
        failed_tasks_count = db.execute(failed_tasks_query).scalar()
    except Exception as e:
        logger.error(f"Error getting failed tasks count: {e}")
        failed_tasks_count = 0

    return templates.TemplateResponse(
        "admin/dashboard.html",
        get_base_context(
            request,
            current_user=current_admin,
            users_count=users_count,
            admins_count=admins_count,
            failed_tasks_count=failed_tasks_count
        )
    )

@router.get("/admin/users", response_class=HTMLResponse)
async def list_users(
    request: Request,
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    List all users in the system
    """
    users = db.query(models.User).all()

    # Get admin status for each user
    admin_user_ids = [
        admin.user_id for admin in db.query(models.AdminUser).all()
    ]

    return templates.TemplateResponse(
        "admin/users.html",
        get_base_context(
            request,
            current_user=current_admin,
            users=users,
            admin_user_ids=admin_user_ids
        )
    )

@router.get("/admin/users/new", response_class=HTMLResponse)
async def new_user_form(
    request: Request,
    current_admin: models.User = Depends(get_current_admin_user)
):
    """
    Form to create a new user
    """
    return templates.TemplateResponse(
        "admin/user_form.html",
        get_base_context(
            request,
            current_user=current_admin,
            user=None
        )
    )

@router.post("/admin/users/new", response_class=HTMLResponse)
async def create_user(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    password: str = Form(...),
    is_admin: bool = Form(False),
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Create a new user
    """
    from app.util.auth import get_password_hash

    # Check if username or email already exists
    existing_username = db.query(models.User).filter(models.User.username == username).first()
    if existing_username:
        return templates.TemplateResponse(
            "admin/user_form.html",
            get_base_context(
                request,
                current_user=current_admin,
                error="Username already exists",
                user=None
            ),
            status_code=400
        )

    existing_email = db.query(models.User).filter(models.User.email == email).first()
    if existing_email:
        return templates.TemplateResponse(
            "admin/user_form.html",
            get_base_context(
                request,
                current_user=current_admin,
                error="Email already exists",
                user=None
            ),
            status_code=400
        )

    # Create the new user
    hashed_password = get_password_hash(password)
    new_user = models.User(
        username=username,
        email=email,
        hashed_password=hashed_password
    )

    db.add(new_user)
    db.commit()
    db.refresh(new_user)

    # Make user an admin if requested
    if is_admin:
        new_admin = models.AdminUser(
            user_id=new_user.id,
            role="admin",
            created_by=current_admin.id
        )
        db.add(new_admin)
        db.commit()

    return RedirectResponse(
        url=f"/admin/users/{new_user.id}",
        status_code=status.HTTP_303_SEE_OTHER
    )

@router.get("/admin/users/{user_id}", response_class=HTMLResponse)
async def view_user(
    request: Request,
    user_id: int,
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    View a specific user's details
    """
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Check if user is an admin
    admin = db.query(models.AdminUser).filter(models.AdminUser.user_id == user_id).first()

    return templates.TemplateResponse(
        "admin/user_detail.html",
        get_base_context(
            request,
            current_user=current_admin,
            user=user,
            is_admin=admin is not None
        )
    )

@router.get("/admin/users/{user_id}/edit", response_class=HTMLResponse)
async def edit_user_form(
    request: Request,
    user_id: int,
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Form to edit an existing user
    """
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Check if user is an admin
    admin = db.query(models.AdminUser).filter(models.AdminUser.user_id == user_id).first()

    return templates.TemplateResponse(
        "admin/user_form.html",
        get_base_context(
            request,
            current_user=current_admin,
            user=user,
            is_admin=admin is not None
        )
    )

@router.post("/admin/users/{user_id}/edit", response_class=HTMLResponse)
async def update_user(
    request: Request,
    user_id: int,
    username: str = Form(...),
    email: str = Form(...),
    password: Optional[str] = Form(None),
    is_admin: bool = Form(False),
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Update an existing user
    """
    from app.util.auth import get_password_hash

    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Check if username already exists (if changed)
    if username != user.username:
        existing_username = db.query(models.User).filter(models.User.username == username).first()
        if existing_username:
            return templates.TemplateResponse(
                "admin/user_form.html",
                get_base_context(
                    request,
                    current_user=current_admin,
                    error="Username already exists",
                    user=user
                ),
                status_code=400
            )

    # Check if email already exists (if changed)
    if email != user.email:
        existing_email = db.query(models.User).filter(models.User.email == email).first()
        if existing_email:
            return templates.TemplateResponse(
                "admin/user_form.html",
                get_base_context(
                    request,
                    current_user=current_admin,
                    error="Email already exists",
                    user=user
                ),
                status_code=400
            )

    # Update user details
    user.username = username
    user.email = email

    if password:
        user.hashed_password = get_password_hash(password)

    db.commit()

    # Update admin status
    admin = db.query(models.AdminUser).filter(models.AdminUser.user_id == user_id).first()

    if is_admin and not admin:
        # Make user an admin
        new_admin = models.AdminUser(
            user_id=user_id,
            role="admin",
            created_by=current_admin.id
        )
        db.add(new_admin)
        db.commit()
    elif not is_admin and admin:
        # Check if this is the last admin
        admin_count = db.query(models.AdminUser).count()
        if admin_count <= 1:
            return templates.TemplateResponse(
                "admin/user_form.html",
                get_base_context(
                    request,
                    current_user=current_admin,
                    error="Cannot remove the last admin user",
                    user=user,
                    is_admin=True
                ),
                status_code=400
            )

        # Remove admin status
        db.delete(admin)
        db.commit()

    return RedirectResponse(
        url=f"/admin/users/{user_id}",
        status_code=status.HTTP_303_SEE_OTHER
    )

@router.post("/admin/users/{user_id}/delete", response_class=HTMLResponse)
async def delete_user(
    request: Request,
    user_id: int,
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Delete a user
    """
    # Prevent deleting yourself
    if user_id == current_admin.id:
        raise HTTPException(
            status_code=400,
            detail="Cannot delete your own account"
        )

    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Check if user is an admin and remove from admin table first
    admin_user = db.query(models.AdminUser).filter(models.AdminUser.user_id == user_id).first()
    if admin_user:
        db.delete(admin_user)

    db.delete(user)
    db.commit()

    return RedirectResponse(
        url="/admin/users",
        status_code=status.HTTP_303_SEE_OTHER
    )

@router.get("/admin/admins", response_class=HTMLResponse)
async def list_admins(
    request: Request,
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    List all admin users
    """
    admin_records = db.query(models.AdminUser).all()

    # Get user details for each admin
    admins = []
    for admin in admin_records:
        user = db.query(models.User).filter(models.User.id == admin.user_id).first()
        if user:
            admins.append({
                "id": admin.id,
                "user_id": admin.user_id,
                "username": user.username,
                "email": user.email,
                "role": admin.role,
                "created_at": admin.created_at
            })

    return templates.TemplateResponse(
        "admin/admins.html",
        get_base_context(
            request,
            current_user=current_admin,
            admins=admins
        )
    )

# REST API endpoints for admin operations
@router.post("/admin/api/admins", response_model=schemas.AdminUserResponse)
async def add_admin_user(
    admin_create: schemas.AdminUserCreate,
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Add a new admin user.
    Only accessible by existing admin users.
    """
    # Check if user exists
    user = db.query(models.User).filter(models.User.id == admin_create.user_id).first()
    if user is None:
        raise HTTPException(status_code=404, detail="User not found")

    # Check if user is already an admin
    existing_admin = db.query(models.AdminUser).filter(
        models.AdminUser.user_id == admin_create.user_id
    ).first()

    if existing_admin:
        raise HTTPException(status_code=400, detail="User is already an admin")

    # Create new admin user
    new_admin = models.AdminUser(
        user_id=admin_create.user_id,
        role=admin_create.role,
        created_by=current_admin.id
    )

    db.add(new_admin)
    db.commit()
    db.refresh(new_admin)

    return {
        "id": new_admin.id,
        "user_id": new_admin.user_id,
        "role": new_admin.role,
        "created_at": new_admin.created_at,
        "created_by": new_admin.created_by
    }

@router.get("/admin/api/admins", response_model=list[schemas.AdminUserResponse])
async def get_all_admin_users_api(
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Get all admin users.
    Only accessible by admin users.
    """
    admin_users = db.query(models.AdminUser).all()
    return admin_users

@router.delete("/admin/api/admins/{admin_id}", response_model=schemas.Message)
async def remove_admin_user_api(
    admin_id: int,
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Remove admin privileges from a user.
    Only accessible by admin users.
    """
    # Get the admin record
    admin_user = db.query(models.AdminUser).filter(models.AdminUser.id == admin_id).first()
    if admin_user is None:
        raise HTTPException(status_code=404, detail="Admin user not found")

    # Check if trying to remove self
    current_admin_record = db.query(models.AdminUser).filter(
        models.AdminUser.user_id == current_admin.id
    ).first()

    if current_admin_record and current_admin_record.id == admin_id:
        raise HTTPException(
            status_code=400,
            detail="Cannot remove your own admin privileges"
        )

    # Check if this is the last admin
    admin_count = db.query(models.AdminUser).count()
    if admin_count <= 1:
        raise HTTPException(
            status_code=400,
            detail="Cannot remove the last admin user"
        )

    # Remove admin privileges
    db.delete(admin_user)
    db.commit()

    return {"message": "Admin privileges removed successfully"}

@router.get("/admin/api/users", response_model=list[schemas.User])
async def get_all_users_api(
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Get all users in the system.
    Only accessible by admin users.
    """
    users = db.query(models.User).all()
    return users

@router.get("/admin/api/users/{user_id}", response_model=schemas.User)
async def get_user_by_id_api(
    user_id: int,
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific user by ID.
    Only accessible by admin users.
    """
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if user is None:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@router.delete("/admin/api/users/{user_id}", response_model=schemas.Message)
async def delete_user_api(
    user_id: int,
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Delete a user from the system.
    Only accessible by admin users.
    """
    # Prevent deleting yourself
    if user_id == current_admin.id:
        raise HTTPException(
            status_code=400,
            detail="Cannot delete your own account"
        )

    user = db.query(models.User).filter(models.User.id == user_id).first()
    if user is None:
        raise HTTPException(status_code=404, detail="User not found")

    # Check if user is an admin and remove from admin table first
    admin_user = db.query(models.AdminUser).filter(models.AdminUser.user_id == user_id).first()
    if admin_user:
        db.delete(admin_user)

    db.delete(user)
    db.commit()

    return {"message": "User deleted successfully"}