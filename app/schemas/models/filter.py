from pydantic import BaseModel, Field, ValidationInfo, field_validator
from typing import Optional
from app.db.models import FilterType

class FilterBase(BaseModel):
    name: str
    filter_type: FilterType = Field(default=FilterType.prompt)
    prompt: Optional[str] = None
    need_details: bool = Field(default=False)
    model: str
    api_base: Optional[str] = None
    api_key: Optional[str] = None

    @field_validator('name', 'model')
    def check_not_empty(cls, v):
        if not v:
            raise ValueError('must not be empty')
        return v

    @field_validator('prompt')
    @classmethod
    def check_prompt_if_prompt_type(cls, value: Optional[str],info: ValidationInfo)->Optional[str]:
        if "filter_type" in info.data and info.data["filter_type"] == FilterType.prompt and not value:
            return None
        return value

class FilterCreate(FilterBase):
    pass

class FilterUpdate(FilterBase):
    pass

class FilterInDBBase(FilterBase):
    id: int

    class Config:
        from_attributes = True

class Filter(FilterInDBBase):
    pass

class FilterInDB(FilterInDBBase):
    pass
