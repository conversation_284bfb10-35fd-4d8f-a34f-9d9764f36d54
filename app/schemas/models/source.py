from pydantic import BaseModel, <PERSON>, field_validator
from app.db.models import SourceProviderType,SourceSubProviderType
from typing import Optional

class SourceProviderBase(BaseModel):
    source_type: SourceProviderType
    source_sub_type: SourceSubProviderType = Field(default=SourceSubProviderType.default)
    nickname: str
    imap_server: Optional[str] = None
    imap_port: Optional[int] = None
    user: Optional[str] = None
    password: Optional[str] = None
    url: Optional[str] = None
    keywords: Optional[str] = None  # 逗号分隔的关键字
    icon_url: Optional[str] = None
    start_time: Optional[int] = None  # 使用时间戳表示

    @field_validator('nickname')
    def check_nickname_not_empty(cls, v):
        if not v:
            raise ValueError('nickname must not be empty')
        return v

    @field_validator('keywords')
    def split_keywords(cls, v):
        if v:
            return [keyword.strip() for keyword in v.split(",")]
        return None

class SourceProviderCreate(SourceProviderBase):
    pass

class SourceProviderUpdate(SourceProviderBase):
    pass

class SourceProviderInDBBase(SourceProviderBase):
    id: int

    class Config:
        from_attributes = True

class SourceProvider(SourceProviderInDBBase):
    pass

class SourceProviderInDB(SourceProviderInDBBase):
    pass