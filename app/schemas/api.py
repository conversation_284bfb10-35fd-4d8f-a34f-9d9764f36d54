from pydantic import BaseModel, model_validator
from typing import List, Generic, TypeVar, Optional
from fastapi import Query
from datetime import datetime
from app.logging import get_logger

from app.db.models import SourceProviderType, SourceSubProviderType

logger = get_logger()

T = TypeVar("T")


class PaginationParams(BaseModel):
    page: int = Query(1, description="Page number", ge=1)
    page_size: int = Query(10, description="Number of items per page", ge=1, le=100)


class PaginatedResponse(BaseModel, Generic[T]):
    items: List[T]
    total: int
    page: int
    page_size: int


class ArticleBase(BaseModel):
    headline: str | None
    blurb: str | None
    link: str | None


class ExtractorResponse(BaseModel):
    is_newsletter: bool
    articles: List[ArticleBase]


class URLBase(BaseModel):
    original_url: str


class URLCreate(URLBase):
    pass


class URLResponse(URLBase):
    short_code: str

    class Config:
        from_attributes = True


class SourceModel(BaseModel):
    source_type: SourceProviderType
    source_sub_type: SourceSubProviderType  # wx_mp,hn,google_news...
    nickname: str  # 填入url后自动获取|邮箱用户，或者用户自定义
    icon_url: str

    class Config:
        from_attributes = True


class AddSourceRequest(SourceModel):
    url: Optional[str] = None
    icon_url: str
    # for email
    imap_server: Optional[str] = None
    imap_port: Optional[int] = None
    user: Optional[str] = None
    password: Optional[str] = None
    # for searchable news website
    keywords: Optional[str] = None  # 用于内置新闻网站，如谷歌新闻，逗号分隔

    @model_validator(mode="after")
    def validate_fields(self) -> "AddSourceRequest":
        logger.info(f"provider_type: {self.source_type}")

        if self.source_type in [SourceProviderType.rss, SourceProviderType.website]:
            if not self.url:
                raise ValueError(
                    f"URL is required for {self.source_type.name} provider"
                )

        if self.source_type == SourceProviderType.email:
            required_fields = {
                "imap_server": self.imap_server,
                "imap_port": self.imap_port,
                "user": self.user,
                "password": self.password,
            }
            missing_fields = [k for k, v in required_fields.items() if not v]
            if missing_fields:
                raise ValueError(
                    f"Fields {', '.join(missing_fields)} are required for email provider"
                )
        return self


class ParseSourceRequest(AddSourceRequest):
    pass


class SourceResponse(SourceModel):
    id: int
    is_folder: int
    parent_id: int
    sort_order: int
    created_at: int


class AddSourceResponse(SourceModel):
    pass


class ParseSourceResponse(SourceModel):
    pass


class DeleteProviderRequest(BaseModel):
    id: int
    name: str


class EmailBase(BaseModel):
    id: int
    subject: str
    sender: str
    sender_address: str
    timestamp: datetime
    content_html: str
    content_md: str


class Email(EmailBase):
    source_id: int

    class Config:
        from_attributes = True


class ReferenceArticles(BaseModel):
    id: int
    user_source_id: int
    title: str
    link: str | None


class TopicSummary(BaseModel):
    id: int
    title: str
    summary: str
    ts: int | None

    reference_articles: List[ReferenceArticles]
    image_url: str | None

class Article(BaseModel):
    id: int
    title: str
    content: str | None  # maybe pdf or github repo
    origin_url: str
    ts: int | None

    class Config:
        from_attributes = True


class CreateFilterSchema(BaseModel):
    filter_type: str
    name: str
    prompt: str
    model: str
    api_base: str | None
    api_key: str | None
    need_details: bool


class CreateFilterCharacterSettingSchema(BaseModel):
    source_ids: List[int]
    filters: List[CreateFilterSchema]
    name: str
    desc: str

class FilterInfo(BaseModel):
    id: int
    name: str


class SourceBase(BaseModel):
    id: int
    source_id: int
    extracted: bool
    ts: Optional[int]

    class Config:
        from_attributes = True


class HackerNewsSourceResponse(SourceBase):
    story_id: int
    title: str
    author: str
    content: Optional[str]
    out_link: Optional[str]
    comments: Optional[str]
    score: Optional[int]


class RssSourceResponse(SourceBase):
    entry_id: str
    link: str
    title: str


class EmailSourceResponse(SourceBase):
    id: int
    subject: str
    sender: str
    sender_address: str
    content_html: str
