from configparser import Converter<PERSON>apping
import uuid
from fastapi import Request, responses
from starlette.middleware.base import BaseHTTPMiddleware
from contextvars import ContextVar
from celery import current_task

# Create a context variable to store the request ID
request_id_var = ContextVar("request_id", default=None)
log_file_var = ContextVar("log_file",default="stdout")

def get_request_id():
    """Get the current request ID from context"""
    return request_id_var.get()

def get_log_file():
    return log_file_var.get()

class RequestIDMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Generate a new UUID for this request
        request_id = str(uuid.uuid4())
        
        # Store it in the context variable
        token = request_id_var.set(request_id)
        
        # Add it to request state
        request.state.request_id = request_id
        
        # Process the request
        response = await call_next(request)
        
        # Add the request ID to the response headers
        response.headers["X-Request-ID"] = request_id
        
        # Reset the context variable
        request_id_var.reset(token)
        
        return response

class LogFileMiddleWare(BaseHTTPMiddleware):
    async def dispatch(self,request: Request,call_next):
        token = log_file_var.set("app")
        response = await call_next(request)
        log_file_var.reset(token)
        return response
        
