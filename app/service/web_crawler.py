import requests
import trafilatura
from app.logging import get_logger

JINA_READER_URL = "https://r.jina.ai"
logger = get_logger()


def convert_webpage_jina(url: str) -> str | None:
    """
    Convert a webpage to markdown using Jin<PERSON>'s Reader API.

    Args:
        url (str): The URL of the webpage to convert.

    Returns:
        str: The converted markdown content.
    """
    try:
        response = requests.get(f"{JINA_READER_URL}/{url}")
        response.raise_for_status()
        return response.text
    except Exception as e:
        logger.error(f"Failed to convert webpage to markdown: {e}")
        return None


def crawl4ai_crawl(url: str) -> str | None:
    # fedora is not supported, skip it, later we can use docker and api to run it
    return None
    # try:
    #     async with AsyncWebCrawler(verbose=True) as crawler:
    #         # Run the crawler on a URL
    #         config = CrawlerRunConfig()
    #         result = await crawler.arun(url=url, crawler_config=config)
    #         if not result.success or result.markdown_v2 is None:
    #             return None
    #         logger.info(
    #             f"LXDEBUG display differnt mark down fit: {result.markdown_v2.fit_markdown}\n raw: {result.markdown_v2.raw_markdown} \n references: {result.markdown_v2.references_markdown}\n cite: {result.markdown_v2.markdown_with_citations} \n html {result.markdown_v2.fit_html}"
    #         )
    #         return result.markdown_v2.fit_markdown
    #
    # except Exception as e:
    #     logger.error(f"Failed to convert webpage to markdown: {e}")
    #     return None


def trafilatura_crawl(url: str) -> str | None:
    try:
        trafilatura_result = trafilatura.fetch_url(url)
        return trafilatura.extract(trafilatura_result)
    except Exception as e:
        logger.error(f"Failed to convert webpage to markdown: {e}")
        return None


def general_crawl(url: str) -> str | None:
    if "/rachelbythebay" in url:
        logger.info(f"Skip rachelbythebay {url}")
        return None
    logger.info(f"Converting webpage to markdown using crawl4ai {url}")
    res = crawl4ai_crawl(url)
    if res is not None:
        return res
    logger.info(f"Failed to convert webpage to markdown using crawl4ai, trying jina")
    res = convert_webpage_jina(url)
    if res is not None:
        return res
    logger.info(f"Failed to convert webpage to markdown using jina, trying trafilatura")
    res = trafilatura_crawl(url)
    if res is not None:
        return res
    logger.info(f"Failed to convert webpage to markdown.")
    return None
