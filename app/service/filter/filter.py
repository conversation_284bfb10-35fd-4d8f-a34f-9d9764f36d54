from fastapi import HTTPException
from typing import List
from app.logging import get_logger
from litellm import completion, litellm

from app.db.models import (
    TopicContent,
)
from app.schemas.models.filter import Filter ,FilterType

# litellm._turn_on_debug()
logger = get_logger()

def translate_en_to_zh(english: str) -> str:
    system_prompt = f"""
    You are a translation expert proficient in various languages, focusing solely on translating text without interpretation. You accurately understand the meanings of proper nouns, idioms, metaphors, allusions, and other obscure words in sentences, translating them appropriately based on the context and language environment. The translation should be natural and fluent. Only return the translated text, without including redundant quotes or additional notes.
    """
    user_input = f"""
    Translate the following English text into Simplified-Chinese text:
    ```
    {english}
    ```
    """
    response = completion(
        model="openai/gpt-4o-mini",
        api_base="https://openrouter.ai/api/v1",
        api_key="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",
        messages=[
            {"content": system_prompt, "role": "system"},
            {"content": user_input, "role": "user"},
        ],
    )
    if response is None:
        raise HTTPException(status_code=500, detail="Failed to get response from llm")

    return response["choices"][0]["message"]["content"].strip()

async def translate_en_to_zh_async(english: str) -> str:
    system_prompt = f"""
    You are a translation expert proficient in various languages, focusing solely on translating text without interpretation. You accurately understand the meanings of proper nouns, idioms, metaphors, allusions, and other obscure words in sentences, translating them appropriately based on the context and language environment. The translation should be natural and fluent. Only return the translated text, without including redundant quotes or additional notes.
    """
    user_input = f"""
    Translate the following English text into Simplified-Chinese text:
    ```
    {english}
    ```
    """
    response = await litellm.acompletion(
        model="openai/gpt-4o-mini",
        api_base="https://openrouter.ai/api/v1",
        api_key="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",
        messages=[
            {"content": system_prompt, "role": "system"},
            {"content": user_input, "role": "user"},
        ],
    )
    if response is None:
        raise HTTPException(status_code=500, detail="Failed to get response from llm")

    return response["choices"][0]["message"]["content"].strip()

def prompt_filter(topic: TopicContent, filter: Filter) -> bool:
    system_prompt = f"""
    You are a help assistant to help user filter information that matches their needs. With given text information, you do a binary classification job based on user's description and Answer only True or False.
    Description: <{filter.prompt}>
    """
    user_input = f"""
    Text: <title: {topic.title}>
    summary: <{topic.summary}>
    """
    response = None
    if filter.model.startswith("openai/"):
        response = completion(
            model=filter.model,
            api_key=filter.api_key,
            api_base=filter.api_base,
            messages=[
                {"content": system_prompt, "role": "system"},
                {"content": user_input, "role": "user"},
            ],
        )
    else:
        response = completion(
            timeout=1000000,
            model=filter.model,
            messages=[
                {"content": system_prompt, "role": "system"},
                {"content": user_input, "role": "user"},
            ],
        )
    if response is None:
        raise HTTPException(status_code=500, detail="Failed to get response from llm")
    if response["choices"][0]["message"]["content"].strip() == "True":
        return True
    elif response["choices"][0]["message"]["content"].strip() == "False":
        return False
    raise HTTPException(
        status_code=500, detail=f"Failed to get response from llm, get {response}"
    )


def keywords_filter(topic: TopicContent, filter: Filter) -> bool:
    pass


def is_wanted(topic: TopicContent, filter: Filter) -> bool:
    match filter.filter_type:
        case FilterType.prompt:
            logger.info(f"is_wanted topic {topic.id} filter {filter.id}")
            return prompt_filter(topic, filter)
        case FilterType.keywords:
            logger.info(f"is_wanted topic {topic.id} filter {filter.id}")
            return keywords_filter(topic, filter)
    raise HTTPException(
        status_code=400, detail=f"Filter type {filter.filter_type} not supported"
    )


def is_wanted_by_filters(topic: TopicContent, filters: List[Filter],fc_id: int) -> bool:
    logger.info(f"filter topic {topic.id} for character {fc_id}")
    for filter in filters:
        if not is_wanted(topic, filter):
            return False
    return True
