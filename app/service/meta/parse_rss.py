#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import asyncio
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import urllib.parse
import tldextract
import aiohttp
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

# import sys
# app project root path
# sys.path.append("/home/<USER>/SHARED/Projects/eyesaver-multi/eyesaver-mono/backend/")
# from app.exceptions import AppException

from rules import SelfRefreshJson

RULE_PROVIDER_URL = "http://100.98.45.17:1200/api/radar/rules"

rss_hub_rules = SelfRefreshJson(RULE_PROVIDER_URL, 60)

# Pydantic Models
class RSSFeed(BaseModel):
    url: str
    title: Optional[str] = None
    image: Optional[str] = None

class PageRSSResponse(BaseModel):
    pageRSS: List[RSSFeed]

class RSSHubResult(BaseModel):
    title: str
    url: str
    path: Optional[str] = None
    isDocs: Optional[bool] = None

class Rule(BaseModel):
    target: Optional[str] = None
    title: Optional[str] = None
    docs: Optional[str] = None
    source: Optional[List[str] | str] = None

class RecognizedRule(BaseModel):
    handler: int
    params: Dict[str, str]

async def fetch_url_content(url: str) -> str:
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            return await response.text()

def parse_rss(xml_content: str) -> Optional[Dict[str, str]]:
    """解析 RSS 内容"""
    import feedparser

    d = feedparser.parse(xml_content)
    if d.bozo:
        return None
    if "title" in d.feed:
        return {"title": d.feed.title}
    return None

def format_blank(str1: Optional[str], str2: Optional[str]) -> str:
    """两个字符串拼接，根据规则添加空格"""
    if str1 and str2:
        if re.search(r'[a-zA-Z0-9]$', str1) or re.search(r'^[a-zA-Z0-9]', str2):
            return str1 + " " + str2
        else:
            return str1 + str2
    else:
        return (str1 or "") + (str2 or "")

def rule_handler(rule: Rule, params: Dict[str, str], url: str, html: str) -> Optional[str]:
    """处理规则并生成 RSS URL"""
    result_with_params = ""
    target = rule.target
    
    if callable(target):
        try:
            result_with_params = target(params, url)
        except Exception:
            result_with_params = ""
    elif isinstance(target, str):
        result_with_params = target

    def replace_brace(match):
        return re.sub(r'\{[^}]*\}', "", match.group(0))
    result_with_params = re.sub(r'/:\w+\{[^}]*\}(?=/|$)', replace_brace, result_with_params)

    if result_with_params:
        found = re.findall(r'/:(\w+\??)(?=/|$)', result_with_params)
        req_list = []
        for p in found:
            if p.endswith('?'):
                req_list.append({"name": p[:-1], "optional": True})
            else:
                req_list.append({"name": p, "optional": False})
        if not req_list:
            return result_with_params

        for param in req_list:
            name = param["name"]
            if name in params and params[name]:
                regex = re.compile(r'/:{}(?:\??)(?=/|$)'.format(re.escape(name)))
                result_with_params = regex.sub("/" + str(params[name]), result_with_params)
            elif param["optional"]:
                regex = re.compile(r'/:{}\?(/.*)?$'.format(re.escape(name)))
                result_with_params = regex.sub("", result_with_params)
                break
            else:
                result_with_params = ""
                break
    return result_with_params if result_with_params else None

def convert_route_to_regex(route: str) -> tuple[re.Pattern, List[str]]:
    """把路由模式转换为正则表达式"""
    route = route.strip("/")
    segments = route.split("/")
    regex_parts = []
    param_names = []
    for seg in segments:
        if seg.startswith(":"):
            name = seg[1:]
            if name.endswith('?'):
                name = name[:-1]
            regex_parts.append("(?P<{}>[^/]+)".format(name))
            param_names.append(name)
        else:
            regex_parts.append(re.escape(seg))
    pattern = "^/" + "/".join(regex_parts) + "$"
    return re.compile(pattern), param_names

def recognize_route(source: str, path: str) -> Optional[Dict[str, str]]:
    """识别路由是否匹配并返回参数"""
    regex, _ = convert_route_to_regex(source)
    m = regex.match(path)
    if m:
        return m.groupdict()
    return None

async def get_page_rss(html: str, location_url: str) -> PageRSSResponse:
    """获取页面中的 RSS 链接"""
    soup = BeautifulSoup(html, "html.parser")
    page_rss: List[RSSFeed] = []

    # 提取默认标题
    default_title = None
    title_tag = soup.find("title")
    if title_tag and title_tag.string:
        default_title = re.sub(
            r"<!\[CDATA\[(.*)]]>", lambda m: m.group(1), title_tag.string
        ).strip()

    # 提取页面图标
    image = None
    icon_link = soup.find("link", rel=re.compile(r"\bicon\b", re.IGNORECASE))
    if icon_link and icon_link.get("href"):
        image = None
    else:
        parsed = urlparse(location_url)
        image = f"https://icons.duckduckgo.com/ip3/{parsed.netloc}.ico"

    def handle_url(url: str) -> str:
        url = re.sub(r"^(feed://)", "https://", url)
        url = re.sub(r"^(feed:)", "", url)
        url = re.sub(r"^(http://)", "https://", url)
        return urljoin(location_url, url)

    if icon_link and icon_link.get("href"):
        image = handle_url(icon_link.get("href"))

    unique: Dict[str, bool] = {}

    def unique_save(url: str):
        key = re.sub(r"^(https?:\/\/|feed:\/\/|feed:)", "", url)
        unique[key] = True

    def unique_check(url: str) -> bool:
        key = re.sub(r"^(https?:\/\/|feed:\/\/|feed:)", "", url)
        return unique.get(key, False)

    # 检测页面是否为 RSS 文档本身
    html_candidate = None
    if soup.body:
        if len(soup.body.contents) > 0:
            first_child = soup.body.contents[0]
            if hasattr(first_child, "name") and first_child.name and first_child.name.lower() == "pre":
                html_candidate = first_child.get_text()
        if not html_candidate:
            viewer = soup.find(id="webkit-xml-viewer-source-xml")
            if viewer:
                html_candidate = viewer.decode_contents()

    if html_candidate:
        result = parse_rss(html_candidate)
        if result:
            feed = RSSFeed(
                url=location_url,
                title=result.get("title", default_title),
                image=image
            )
            page_rss.append(feed)
            return PageRSSResponse(pageRSS=page_rss)

    # RSS MIME types
    types = [
        "application/rss+xml",
        "application/atom+xml",
        "application/rdf+xml",
        "application/rss",
        "application/atom",
        "application/rdf",
        "text/rss+xml",
        "text/atom+xml",
        "text/rdf+xml",
        "text/rss",
        "text/atom",
        "text/rdf",
        "application/feed+json",
    ]

    # 处理 head 内的 link 标签
    link_tags = soup.find_all("link", attrs={"type": True})
    for link in link_tags:
        type_attr = link.get("type")
        if type_attr in types:
            feed_url = link.get("href")
            if feed_url:
                feed_url = handle_url(feed_url)
                feed = RSSFeed(
                    url=feed_url,
                    title=link.get("title") or default_title,
                    image=image
                )
                if not unique_check(feed.url):
                    page_rss.append(feed)
                    unique_save(feed.url)

    # 处理以 "feed:" 前缀的 a 标签
    feed_a_tags = soup.find_all("a", href=lambda h: h and h.startswith("feed:"))
    for a in feed_a_tags:
        href = a.get("href")
        if href:
            feed = RSSFeed(
                url=handle_url(href),
                title=a.get("title") or default_title,
                image=image
            )
            if not unique_check(feed.url):
                page_rss.append(feed)
                unique_save(feed.url)

    # 处理普通 a 标签
    a_tags = soup.find_all("a", href=True)
    check_regex = re.compile(r"([^a-zA-Z]|^)rss([^a-zA-Z]|$)", re.IGNORECASE)
    uncertain: List[RSSFeed] = []
    feed_link_regex = re.compile(r"/(feed|rss|atom)(\.(xml|rss|atom))?\/?$")

    for a in a_tags:
        href = a.get("href")
        if href:
            cond1 = feed_link_regex.search(href)
            cond2 = a.get("title") and check_regex.search(a.get("title"))
            cond3 = a.get("class") and any(
                check_regex.search(cls)
                for cls in (
                    a.get("class")
                    if isinstance(a.get("class"), list)
                    else [a.get("class")]
                )
            )
            cond4 = a.get_text() and check_regex.search(a.get_text())
            if cond1 or cond2 or cond3 or cond4:
                feed = RSSFeed(
                    url=handle_url(href),
                    title=a.get_text() or a.get("title") or default_title,
                    image=image
                )
                if not unique_check(feed.url):
                    uncertain.append(feed)

    # 异步验证 uncertain 阵列中的 RSS 链接
    async def validate_feed(feed: RSSFeed):
        try:
            content = await fetch_url_content(feed.url)
            result = parse_rss(content)
            if result:
                if result.get("title"):
                    feed.title = result["title"]
                page_rss.append(feed)
                unique_save(feed.url)
        except Exception:
            pass

    if uncertain:
        await asyncio.gather(*(validate_feed(feed) for feed in uncertain))

    return PageRSSResponse(pageRSS=page_rss)

def get_page_rsshub(data: Dict[str, Any]) -> List[RSSHubResult]:
    """根据页面数据解析出 RSS 地址"""
    url = data.get("url")
    html = data.get("html")
    rules = data.get("rules")
    try:
        parsed_url = urllib.parse.urlparse(url)
    except Exception:
        return []

    extracted = tldextract.extract(url)
    if extracted.domain and extracted.suffix:
        domain = extracted.domain + "." + extracted.suffix
    else:
        return []
    subdomain = extracted.subdomain

    if domain not in rules:
        return []
    domain_rules = rules[domain]

    rule_list = None
    if subdomain in domain_rules:
        rule_list = domain_rules[subdomain]
    else:
        if "." in domain_rules:
            rule_list = domain_rules["."]
        elif "www" in domain_rules:
            rule_list = domain_rules["www"]
    if not rule_list:
        return []

    recognized: List[RecognizedRule] = []
    path = parsed_url.path.rstrip("/") or "/"

    for index, ru in enumerate(rule_list):
        rule = Rule(**ru)
        ori_sources = []
        source_val = rule.source
        if isinstance(source_val, list):
            ori_sources = source_val
        elif isinstance(source_val, str):
            ori_sources = [source_val]
        sources = []

        for source in ori_sources:
            source_clean = re.sub(r'(\/:\w+)\?(?=/|$)', r'\1', source)
            sources.append(source_clean)
            temp_source = source_clean
            while True:
                tail_match = re.search(r'/:\w+$', temp_source)
                if tail_match:
                    temp_source = temp_source[:-len(tail_match.group(0))]
                    sources.append(temp_source)
                else:
                    break

        sources = list(dict.fromkeys(sources))
        for source in sources:
            params = recognize_route(source, path)
            if params is not None:
                recognized.append(RecognizedRule(handler=index, params=params))

    result: List[RSSHubResult] = []
    for recog in recognized:
        rule_obj = Rule(**rule_list[recog.handler])
        parsed_route = rule_handler(rule_obj, recog.params, url, html)
        if parsed_route:
            title_prefix = "Current" if domain_rules.get("_name") else ""
            result.append(RSSHubResult(
                title=format_blank(title_prefix, rule_obj.title),
                url="{rsshubDomain}" + parsed_route,
                path=parsed_route
            ))
        else:
            result.append(RSSHubResult(
                title=format_blank("Current" if domain_rules.get("_name") else "", rule_obj.title),
                url=rule_obj.docs,
                isDocs=True
            ))
    return result

def get_website_rsshub(data: Dict[str, Any]) -> List[RSSHubResult]:
    """根据网站 URL 生成网站所有规则的列表"""
    url = data.get("url")
    rules = data.get("rules")
    try:
        parsed_url = urllib.parse.urlparse(url)
    except Exception:
        return []
    extracted = tldextract.extract(url)
    if extracted.domain and extracted.suffix:
        domain = extracted.domain + "." + extracted.suffix
    else:
        return []
    if domain not in rules:
        return []
    domain_rules = rules[domain]
    domain_rules_list = []
    for sub_key, rules_list in domain_rules.items():
        # 忽略 "_开头" 的键（如 _name）
        if not sub_key.startswith("_"):
            domain_rules_list.extend(rules_list)
    result = []
    for rule in domain_rules_list:
        title = format_blank(domain_rules.get("_name", ""), rule.get("title"))
        result.append(RSSHubResult(
            title=title,
            url=rule.get("docs"),
            isDocs=True
        ))
    return result

async def get_rss_from_url(url: str) -> List[RSSFeed]:
    """从指定 URL 获取可订阅该网站内容的 RSS 链接。

    Args:
        url (str): 网站、社媒、博客等页面的 URL，通常内容是列表或者时间线。

    Returns:
        List[RSSFeed]: 用于订阅该网站的 RSS 信息。
    """
    result = []
    try:
        # 使用 aiohttp 异步获取页面 HTML 内容
        html_content = await fetch_url_content(url)
       
        # 获取页面中的 RSS 链接
        page_rss = await get_page_rss(html_content, url)
        if page_rss.pageRSS:
            for i in page_rss:
                print(i)
                result.append(i)

        # 尝试从 RSSHub 获取
        page_rss_hub = get_page_rsshub({
            "url": url, 
            "html": html_content, 
            "rules": rss_hub_rules.get_data()
        })
        if page_rss_hub:
            for i in page_rss_hub:
                print(i)
                result.append(RSSFeed(
                    url=i.url,
                    title=i.title,
                    image=None
                ))

        # 尝试获取网站级别的 RSSHub 规则
        website_rss_hub = get_website_rsshub({
            "url": url, 
            "rules": rss_hub_rules.get_data()
        })
        if website_rss_hub:
            for i in website_rss_hub:
                print(i)
                result.append(RSSFeed(
                    url=i.url,
                    title=i.title,
                    image=None
                ))
        return result
    except aiohttp.ClientError as e:
        print(f"错误：无法访问该URL - {e}")
        return []
        # raise AppException(e) from e
    except Exception as e:
        print(f"发生错误：{e}")
        return []

if __name__ == "__main__":
    import sys

    # 从用户输入获取页面URL
    print("请输入要检查的网页URL（例如：https://example.com）：")
    url = input().strip()
    
    # 验证URL格式
    if not url.startswith(('http://', 'https://')):
        print("错误：URL必须以 http:// 或 https:// 开头")
        sys.exit(1)

    async def main():
        result = await get_rss_from_url(url)
        if result:
            for rss in result:
                print(rss)
                print(f"RSS 链接：{rss.url}")
                print(f"标题: {rss.title}")
                if rss.image:
                    print(f"图标: {rss.image}")
        else:
            print("\n未找到任何 RSS 链接")

    asyncio.run(main())
