#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import threading
import time
import requests

class SelfRefreshJson:
    def __init__(self, url, refresh_interval=60):
        """
        参数说明：
          url: 获取 JSON 数据的网址
          refresh_interval: 数据更新的间隔，单位：秒（默认60秒）
        """
        self.url = url
        self.refresh_interval = refresh_interval

        # 保存 JSON 数据，初始为空字典，可根据实际需求调整
        self.data = {}
        # 用于保护 data 的线程锁
        self.data_lock = threading.Lock()
        # 用于停止后台线程的事件
        self._stop_event = threading.Event()

        # 启动后台线程，定期更新数据
        self._thread = threading.Thread(target=self._update_loop, name="JsonDataUpdateThread")
        self._thread.daemon = True  # 后台线程，主线程退出时会自动结束
        self._thread.start()

    def _update_loop(self):
        """
        后台线程循环：每 refresh_interval 秒获取一次数据
        """
        while not self._stop_event.is_set():
            self._fetch_data()
            # 使用 wait 而非 time.sleep，这样 stop() 时可以更快退出
            self._stop_event.wait(self.refresh_interval)

    def _fetch_data(self):
        """
        请求服务器，获取 JSON 数据，并更新到 self.data 中
        """
        try:
            response = requests.get(self.url, timeout=10)
            if response.status_code == 200:
                # 尝试解析 JSON 数据
                json_data = response.json()
                # 更新共享数据时加锁，保证线程安全
                with self.data_lock:
                    self.data = json_data
                print("数据已更新。")
            else:
                print("请求失败，状态码：", response.status_code)
        except Exception as e:
            # 捕获请求异常（比如网络异常、解析异常等）
            print("获取 JSON 数据时发生异常:", e)

    def get_data(self):
        """
        获取当前保存的 JSON 数据（返回副本，防止外部直接修改）
        """
        with self.data_lock:
            # 如果数据为字典则返回浅拷贝，否则直接返回（也可以根据需求进行深拷贝）
            if isinstance(self.data, dict):
                return self.data.copy()
            return self.data

    def stop(self):
        """
        停止后台自动更新线程
        """
        self._stop_event.set()
        self._thread.join()