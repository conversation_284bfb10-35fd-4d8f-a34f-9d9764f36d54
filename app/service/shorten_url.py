import string
import random
from sqlalchemy.orm import Session
from sqlalchemy import select
from fastapi import Depends
from app.db.models import URL
from app.db.session import get_db


def generate_short_code() -> str:
    characters = string.ascii_letters + string.digits
    return "".join(random.choice(characters) for _ in range(6))


def shorten_url(url: str, session: Session) -> str:
    short_code = generate_short_code()
    db_url = URL(original_url=url, short_code=short_code)
    session.add(db_url)
    session.commit()
    return short_code


def restore_url(short_code: str, session: Session) -> str | None:
    statement = select(URL.original_url).where(URL.short_code == short_code).limit(1)
    complete_url = session.execute(statement).scalar_one_or_none()
    if complete_url is None:
        return None
    return complete_url
