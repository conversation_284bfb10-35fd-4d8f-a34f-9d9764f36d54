import requests
import xml.etree.ElementTree as ET
from typing import Dict, Optional
from app.util.llms import extract_feed_info

class RSSParser:
    def _extract_from_xml(self, xml_content: str) -> Optional[Dict[str, str]]:
        try:
            root = ET.fromstring(xml_content)
            
            # Handle RSS format
            if root.tag == 'rss':
                channel = root.find('channel')
                if channel is not None:
                    return {
                        'title': channel.find('title').text if channel.find('title') is not None else '',
                        'description': channel.find('description').text if channel.find('description') is not None else '',
                        'link': channel.find('link').text if channel.find('link') is not None else ''
                    }
            
            # Handle Atom format
            elif root.tag.endswith('feed'):
                # Try to extract title
                title = ''
                title_elem = root.find('.//{http://www.w3.org/2005/Atom}title')
                if title_elem is not None:
                    title = title_elem.text
                
                # Try to extract description
                description = ''
                subtitle_elem = root.find('.//{http://www.w3.org/2005/Atom}subtitle')
                if subtitle_elem is not None:
                    description = subtitle_elem.text
                
                # Try to extract link
                link = ''
                # First try alternate link
                link_elem = root.find(".//{http://www.w3.org/2005/Atom}link[@rel='alternate']")
                if link_elem is not None:
                    link = link_elem.get('href', '')
                # Then try any link
                if not link:
                    link_elem = root.find('.//{http://www.w3.org/2005/Atom}link')
                    if link_elem is not None:
                        link = link_elem.get('href', '')
                
                # For GitHub feeds, extract username from URL
                if 'github.com' in link:
                    try:
                        username = link.split('github.com/')[1].split('/')[0]
                        title = f"{username}'s Activity"
                        description = f"{username}'s public activity on GitHub"
                    except:
                        pass

                return {
                    'title': title,
                    'description': description,
                    'link': link
                }
            
            return None
        except Exception as e:
            print(f"XML parsing error: {e}")
            return None

    def parse(self, url: str) -> Dict[str, str]:
        try:
            response = requests.get(url)
            content = response.text

            # First try XML parsing
            result = self._extract_from_xml(content)
            
            # If XML parsing fails or misses required fields, use LLM
            if result is None or not result['title'] or not result['link']:
                result = extract_feed_info(content)

            return result
        except Exception as e:
            print(f"Error fetching URL {url}: {e}")
            return {'title': '', 'description': '', 'link': ''}
