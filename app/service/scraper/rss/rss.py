from datetime import datetime
from app.logging import get_logger
from sqlalchemy.orm import Session
from fastapi import HTTPException
import feedparser
import aiohttp
from typing import List, Optional, Tuple

from app.db.pagination import paginate_query, PaginationDirection
from app.util.get_favorite_icon import get_favicon_url
from app.service.scraper.rss.meta_parser import RSSParser
from app.db.models import RssSource

logger = get_logger()

async def get_rss_metadata(
    url: str,
) -> Tuple[Optional[str], Optional[str], Optional[str]]:
    """
    从RSS源获取元数据（RSS源名称和图标URL）
    如果无法获取图标，则使用 ui-avatars.com 生成一个基于名称的头像

    Args:
        url (str): RSS源URL

    Returns:
        Tuple[Optional[str], Optional[str], Optional[str]]: (RSS源名称, RSS介绍, 图标URL)
    """
    result = RSSParser().parse(url)
    icon_url = get_favicon_url(result["link"])
    if not icon_url:
        icon_url = f"https://ui-avatars.com/api/?name={result['title']}&random=true"
    return result["title"], result["description"], icon_url




async def list_rss_source(
    source_id: int, limit: int, cursor: int | None, direction: str, db: Session
) -> list[RssSource]:
    """
    获取 RSS 源列表

    Args:
        source_id: Source ID
        limit: 每页数量
        cursor: 分页游标
        direction: 分页方向("next"或"prev")
        db: 数据库会话

    Returns:
        list[RssSource]: RSS 源列表

    Raises:
        HTTPException: 当参数验证失败或发生其他错误时抛出
    """
    try:
        # 将字符串方向转换为枚举
        pagination_direction = (
            PaginationDirection.NEXT
            if direction == "next"
            else PaginationDirection.PREV
        )

        # 使用通用分页查询
        result = await paginate_query(
            db=db,
            model=RssSource,
            source_id=source_id,
            limit=limit,
            cursor=cursor,
            direction=pagination_direction,
        )

        return result.items

    except ValueError as e:
        logger.error(f"Error listing RSS sources: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error listing RSS sources: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
