import json
import unittest
from rss_parser import RSSParser

class TestRSSParser(unittest.TestCase):
    def setUp(self):
        self.parser = RSSParser()
        with open('test.json', 'r') as f:
            self.test_data = json.load(f)

    def test_all_cases(self):
        for test_case in self.test_data['testCases']:
            url = test_case['url']
            expected = test_case['expected']
            
            result = self.parser.parse(url)
            
            # Test title
            self.assertEqual(
                result['title'],
                expected['title'],
                f"Title mismatch for {url}. Expected: {expected['title']}, Got: {result['title']}"
            )
            
            # Test link
            self.assertEqual(
                result['link'],
                expected['link'],
                f"Link mismatch for {url}. Expected: {expected['link']}, Got: {result['link']}"
            )
            
            # Test description if it exists in expected
            if 'description' in expected and expected['description']:
                self.assertEqual(
                    result['description'],
                    expected['description'],
                    f"Description mismatch for {url}. Expected: {expected['description']}, Got: {result['description']}"
                )

if __name__ == '__main__':
    unittest.main()
