from app.logging import get_logger
from fastapi import HTTPException

from app.db.models import (
    HackerNewsSource,
)
from app.db.session import Session
from app.db.pagination import paginate_query, PaginationDirection

logger = get_logger()

async def list_hn_source(
    source_id: int, limit: int, cursor: int | None, direction: str, db: Session
) -> list[HackerNewsSource]:
    """
    获取Hacker News源列表

    Args:
        source_id: Source ID
        limit: 每页数量
        cursor: 分页游标
        direction: 分页方向("next"或"prev")
        db: 数据库会话

    Returns:
        list[HackerNewsSource]: Hacker News源列表

    Raises:
        HTTPException: 当参数验证失败或发生其他错误时抛出
    """
    try:
        # 将字符串方向转换为枚举
        pagination_direction = (
            PaginationDirection.NEXT
            if direction == "next"
            else PaginationDirection.PREV
        )

        # 使用通用分页查询
        result = await paginate_query(
            db=db,
            model=HackerNewsSource,
            source_id=source_id,
            limit=limit,
            cursor=cursor,
            direction=pagination_direction,
        )

        return result.items

    except ValueError as e:
        logger.error(f"Error listing Hacker News sources: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error listing Hacker News sources: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
