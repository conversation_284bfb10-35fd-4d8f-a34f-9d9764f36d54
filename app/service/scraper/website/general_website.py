from typing import Optional, <PERSON><PERSON>

from app.util.get_favorite_icon import get_favicon_url
from app.util.get_page_title import get_page_title

async def get_website_metadata(url: str) -> Tuple[Optional[str], Optional[str]]:
    """
    从网站获取元数据（网站名称和图标URL）
    如果无法获取图标，则使用 ui-avatars.com 生成一个基于网站名的头像

    Args:
        url (str): 网站URL

    Returns:
        Tuple[Optional[str], Optional[str]]: (网站名称, 图标URL)
    """
    title = get_page_title(url)
    icon_url = get_favicon_url(url)
    if not icon_url:
        icon_url = f"https://ui-avatars.com/api/?name={title}&random=true"
    return title, icon_url