from fastapi import HTTPException
from app.schemas.api import ArticleBase, ExtractorResponse
from app.util.dify_client.model import ChunkCompletionResponseModel
from app.logging import get_logger
import json_repair

logger = get_logger()

async def parse_classify_result(
    classification: ChunkCompletionResponseModel,
) -> ExtractorResponse:
    data = classification.answer
    logger.debug(f"Classification response: {data}")
    answer = json_repair.loads(data)
    is_newsletter: bool = False
    articles = []
    if not isinstance(answer, dict) or "is_newsletter" not in answer:
        raise HTTPException(status_code=500, detail="Invalid classification response")

    is_newsletter = answer["is_newsletter"]
    if is_newsletter:
        if "articles" not in answer:
            raise HTTPException(
                status_code=500, detail="Invalid classification response"
            )
        for article in answer["articles"]:
            # do not validate article, downstream should do the validation
            logger.debug(f"Article: {article}")
            articles.append(
                ArticleBase(
                    headline=article["headline"],
                    blurb=article["blurb"],
                    link=article["link"],
                )
            )
    else:
        is_newsletter = False

    response = ExtractorResponse(
        is_newsletter=is_newsletter,
        articles=articles,
    )
    logger.info(f"Response: {response}")
    return response
