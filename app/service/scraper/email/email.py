import json
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import select
from sqlalchemy.orm import Session

from app.db.models import (
    EmailSource,
    SourceProvider,
)
from app.db.session import get_db
from app.logging import get_logger

router = APIRouter()
logger = get_logger()


def list_mailbox(source_id: int, page_size: int, page_num: int, db: Session):
    provider = db.get(SourceProvider, source_id)
    if provider is None:
        raise HTTPException(status_code=404, detail=f"Provider {source_id} not found")

    stmt = (
        select(EmailSource)
        .where(EmailSource.source_id == source_id)
        .order_by(EmailSource.ts.desc())
        .limit(page_size)
        .offset((page_num - 1) * page_size)
    )

    results = db.execute(stmt).scalars()

    logger.info(
        f"LXDEBUG List mailbox result: {json.dumps([[field, getattr(email, field).isoformat() if isinstance(getattr(email, field), datetime) else getattr(email, field)] for email in results for field in email.__dict__.keys() if field != '_sa_instance_state' and field != 'content_html'])}"
    )
    return results


@router.get("/{uid}")
def view_email(uid: str, db: Session = Depends(get_db)):
    stmt = select(EmailSource).where(EmailSource.id == uid)
    email = db.execute(stmt).scalar_one_or_none()
    if email is None:
        raise HTTPException(status_code=404, detail="Email not found")
    return email
