/* Admin Panel CSS - Modern UI */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --primary-color: #4361ee;
  --primary-hover: #3a56e4;
  --secondary-color: #f8f9fa;
  --text-color: #1a202c;
  --text-muted: #64748b;
  --danger-color: #e53e3e;
  --danger-hover: #c53030;
  --success-color: #38a169;
  --success-hover: #2f855a;
  --warning-color: #f59e0b;
  --info-color: #0284c7;
  --border-color: #e2e8f0;
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --transition-speed: 0.2s;
  --sidebar-width: 250px;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  color: var(--text-color);
  background-color: #f1f5f9;
  margin: 0;
  padding: 0;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 1.5rem;
}

/* Header and Navigation */
.admin-header {
  background-color: white;
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.admin-header h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  color: var(--primary-color);
  display: flex;
  align-items: center;
}

.admin-header h1::before {
  content: '';
  display: inline-block;
  width: 24px;
  height: 24px;
  margin-right: 8px;
  background-color: var(--primary-color);
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 4.354a4 4 0 110 5.292V12M12 12v7M21 12H3'/%3E%3C/svg%3E");
  mask-size: contain;
  mask-repeat: no-repeat;
}

.admin-nav {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.admin-nav a {
  color: var(--text-color);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-speed);
  position: relative;
  padding: 0.5rem 0;
}

.admin-nav a:hover {
  color: var(--primary-color);
}

.admin-nav a.active {
  color: var(--primary-color);
}

.admin-nav a.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

/* User Menu */
.user-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: var(--secondary-color);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
}

.user-menu span {
  font-weight: 500;
  color: var(--text-color);
}

/* Dashboard Stats */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.stat-card {
  background-color: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: var(--card-shadow);
  display: flex;
  flex-direction: column;
  transition: transform var(--transition-speed), box-shadow var(--transition-speed);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.stat-card h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-muted);
  margin: 0 0 0.75rem 0;
  display: flex;
  align-items: center;
}

.stat-card h3::before {
  content: '';
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  background-color: var(--text-muted);
  mask-size: contain;
  mask-repeat: no-repeat;
}

.stat-card:nth-child(1) h3::before {
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2'/%3E%3Ccircle cx='12' cy='7' r='4'/%3E%3C/svg%3E");
}

.stat-card:nth-child(2) h3::before {
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 4.354a4 4 0 110 5.292V12M12 12v7M21 12H3'/%3E%3C/svg%3E");
}

.stat-card .number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.stat-card .actions {
  margin-top: auto;
}

/* Tables */
.table-container {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: var(--card-shadow);
  margin-top: 2rem;
  overflow: hidden;
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
}

.admin-table th {
  text-align: left;
  padding: 1rem 1.5rem;
  font-weight: 600;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-muted);
  background-color: var(--secondary-color);
  position: sticky;
  top: 0;
}

.admin-table td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.admin-table tr:last-child td {
  border-bottom: none;
}

.admin-table tr:hover {
  background-color: var(--secondary-color);
}

/* Badges */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  padding: 0.35rem 0.85rem;
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.badge-primary {
  background-color: rgba(67, 97, 238, 0.1);
  color: var(--primary-color);
}

.badge-success {
  background-color: rgba(56, 161, 105, 0.1);
  color: var(--success-color);
}

.badge-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.badge-info {
  background-color: rgba(2, 132, 199, 0.1);
  color: var(--info-color);
}

/* Forms */
.form-container {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: var(--card-shadow);
  padding: 2rem;
  margin-top: 2rem;
  max-width: 600px;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-color);
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  transition: all var(--transition-speed);
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
}

.form-check {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.form-check-input {
  margin-right: 0.5rem;
  width: 1.25rem;
  height: 1.25rem;
}

.form-error {
  color: var(--danger-color);
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

/* Notifications */
.notification {
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
  animation: fadeIn 0.3s ease-out;
  display: flex;
  align-items: center;
  border-left: 4px solid var(--primary-color);
}

.notification-info {
  background-color: rgba(2, 132, 199, 0.1);
  border-left-color: var(--info-color);
}

.notification-error {
  background-color: rgba(229, 62, 62, 0.1);
  border-left-color: var(--danger-color);
}

.notification-success {
  background-color: rgba(56, 161, 105, 0.1);
  border-left-color: var(--success-color);
}

.notification-hide {
  animation: fadeOut 0.3s ease-in forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(-10px); }
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  padding: 0.625rem 1.25rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all var(--transition-speed);
  text-decoration: none;
  font-size: 0.875rem;
  gap: 0.5rem;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: var(--danger-hover);
  transform: translateY(-1px);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: var(--success-hover);
  transform: translateY(-1px);
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  border-radius: 0.375rem;
}

.btn-icon {
  padding: 0.5rem;
}

.btn-icon svg {
  width: 1.25rem;
  height: 1.25rem;
}

/* Action buttons and containers */
.actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  align-items: center;
}

.actions-stacked {
  flex-direction: column;
  align-items: flex-start;
}

/* Detail pages */
.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.detail-item {
  margin-bottom: 1.5rem;
}

.detail-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-muted);
  margin-bottom: 0.5rem;
}

.detail-value {
  font-size: 1rem;
  color: var(--text-color);
}

/* Header with action buttons */
.header-with-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.header-with-actions h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.admin-actions {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: var(--card-shadow);
  padding: 1.5rem;
  margin-top: 2rem;
}

.admin-actions h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-color);
}

/* Footer */
.admin-footer {
  background-color: white;
  border-top: 1px solid var(--border-color);
  padding: 1.5rem 0;
  margin-top: 3rem;
  color: var(--text-muted);
  font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .actions {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .admin-nav {
    display: none;
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    background-color: white;
    flex-direction: column;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  
  .admin-nav.show {
    display: flex;
  }
  
  .user-menu {
    margin-left: auto;
  }
  
  .admin-header {
    padding: 0.75rem 1rem;
  }
  
  .admin-header h1 {
    font-size: 1.25rem;
  }
  
  .menu-toggle {
    display: block;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    margin-right: 0.5rem;
  }
  
  .header-with-actions {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .container {
    padding: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Modal dialog */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal-container {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  transform: translateY(20px);
  transition: transform 0.3s;
}

.modal-overlay.active .modal-container {
  transform: translateY(0);
}

.modal-content {
  padding: 1.5rem;
}

.modal-content p {
  margin-bottom: 1.5rem;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* Tooltips */
.tooltip {
  position: absolute;
  z-index: 1000;
  background-color: #1a202c;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.2s, transform 0.2s;
  pointer-events: none;
  text-align: center;
  max-width: 250px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.tooltip::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #1a202c;
}

.tooltip.show {
  opacity: 0.9;
  transform: translateY(0);
}

/* Notifications container */
.notifications-container {
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  z-index: 1000;
  pointer-events: none;
}

.notification {
  pointer-events: auto;
  max-width: 350px;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem 1.25rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateX(100%);
  opacity: 0;
  transition: transform 0.3s, opacity 0.3s;
}

.notification.show {
  transform: translateX(0);
  opacity: 1;
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: inherit;
}

.notification-info {
  border-left: 4px solid var(--primary-color);
}

.notification-success {
  border-left: 4px solid var(--success-color);
}

.notification-error {
  border-left: 4px solid var(--danger-color);
}

.notification-hide {
  transform: translateX(100%);
  opacity: 0;
}

/* Table sorting controls */
th.sortable {
  cursor: pointer;
  position: relative;
  padding-right: 2rem !important;
}

.sort-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 0.75rem;
  height: 0.75rem;
  display: inline-block;
}

.sort-icon::before,
.sort-icon::after {
  content: '';
  position: absolute;
  left: 0;
  width: 0;
  height: 0;
}

.sort-icon::before {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid var(--border-color);
  top: 0;
}

.sort-icon::after {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid var(--border-color);
  bottom: 0;
}

th[data-sort="asc"] .sort-icon::before {
  border-bottom-color: var(--primary-color);
}

th[data-sort="desc"] .sort-icon::after {
  border-top-color: var(--primary-color);
}

/* Loading spinner */
.is-loading {
  position: relative;
  color: transparent !important;
}

.is-loading .spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 1.25rem;
  height: 1.25rem;
  margin: -0.625rem 0 0 -0.625rem;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-top-color: white;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Invalid form fields */
.form-control.is-invalid {
  border-color: var(--danger-color);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24' fill='none' stroke='%23e53e3e' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1.25rem;
  padding-right: 2.5rem;
}

/* Page transitions */
.loaded .stat-card,
.loaded .table-container,
.loaded .admin-actions,
.loaded .form-container {
  transition: transform 0.3s, box-shadow 0.3s, opacity 0.3s !important;
}

/* Card grid for dashboard */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.dashboard-grid .card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
}

.dashboard-grid .card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dashboard-grid .card-header {
  background-color: var(--primary-color);
  color: white;
  padding: 1rem 1.5rem;
  font-weight: 600;
}

.dashboard-grid .card-body {
  padding: 1.5rem;
}

/* Data visualization elements */
.chart-container {
  position: relative;
  height: 250px;
  margin-top: 1rem;
}

.progress-container {
  margin-bottom: 1rem;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.375rem;
  font-size: 0.875rem;
}

.progress-bar {
  height: 0.5rem;
  background-color: var(--border-color);
  border-radius: 0.25rem;
  overflow: hidden;
}

.progress-value {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 0.25rem;
  transition: width 0.5s;
}

/* Hover effects for UI elements */
.btn:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.admin-table td .btn {
  opacity: 0.8;
  transition: opacity 0.2s;
}

.admin-table tr:hover td .btn {
  opacity: 1;
} 