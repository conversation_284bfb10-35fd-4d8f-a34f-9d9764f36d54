// Admin Panel JavaScript with enhanced modern interactions

document.addEventListener('DOMContentLoaded', function() {
    // Initialize interactive elements
    initDeleteConfirmations();
    highlightCurrentNav();
    initMobileMenu();
    initFormValidation();
    setupApiInteractions();
    initTooltips();
    initTableSorting();
    setupDashboardAnimations();
    
    // Add transition class to body after load
    setTimeout(() => {
        document.body.classList.add('loaded');
    }, 100);
});

// Mobile menu toggle
function initMobileMenu() {
    const menuToggle = document.createElement('button');
    menuToggle.classList.add('menu-toggle');
    menuToggle.setAttribute('aria-label', 'Toggle navigation menu');
    menuToggle.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="3" y1="12" x2="21" y2="12"></line>
            <line x1="3" y1="6" x2="21" y2="6"></line>
            <line x1="3" y1="18" x2="21" y2="18"></line>
        </svg>
    `;
    
    const header = document.querySelector('.admin-header');
    const nav = document.querySelector('.admin-nav');
    
    if (header && nav) {
        header.insertBefore(menuToggle, header.firstChild);
        
        menuToggle.addEventListener('click', function() {
            nav.classList.toggle('show');
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (nav.classList.contains('show') && !nav.contains(e.target) && !menuToggle.contains(e.target)) {
                nav.classList.remove('show');
            }
        });
    }
}

// Handle confirmation for delete actions with modern dialog
function initDeleteConfirmations() {
    document.body.addEventListener('click', function(e) {
        const target = e.target;
        
        // Check if the clicked element has a data-confirm attribute
        if (target.hasAttribute('data-confirm')) {
            e.preventDefault();
            
            const message = target.getAttribute('data-confirm');
            const form = target.closest('form');
            
            // Create and show a custom confirmation modal
            showConfirmationModal(message, function() {
                if (form) {
                    form.submit();
                }
            });
        }
    });
}

// Create and display a custom confirmation modal
function showConfirmationModal(message, onConfirm) {
    // Create modal elements
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'modal-overlay';
    
    const modalContainer = document.createElement('div');
    modalContainer.className = 'modal-container';
    
    const modalContent = document.createElement('div');
    modalContent.className = 'modal-content';
    
    const modalMessage = document.createElement('p');
    modalMessage.textContent = message;
    
    const modalActions = document.createElement('div');
    modalActions.className = 'modal-actions';
    
    const cancelButton = document.createElement('button');
    cancelButton.className = 'btn';
    cancelButton.textContent = 'Cancel';
    
    const confirmButton = document.createElement('button');
    confirmButton.className = 'btn btn-danger';
    confirmButton.textContent = 'Confirm';
    
    // Build the modal structure
    modalActions.appendChild(cancelButton);
    modalActions.appendChild(confirmButton);
    
    modalContent.appendChild(modalMessage);
    modalContent.appendChild(modalActions);
    
    modalContainer.appendChild(modalContent);
    modalOverlay.appendChild(modalContainer);
    
    // Add to document
    document.body.appendChild(modalOverlay);
    
    // Add animation class
    setTimeout(() => {
        modalOverlay.classList.add('active');
    }, 10);
    
    // Handle button clicks
    cancelButton.addEventListener('click', () => {
        closeModal(modalOverlay);
    });
    
    confirmButton.addEventListener('click', () => {
        closeModal(modalOverlay);
        onConfirm();
    });
    
    // Close when clicking outside
    modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
            closeModal(modalOverlay);
        }
    });
    
    // Close on Escape key
    document.addEventListener('keydown', function escListener(e) {
        if (e.key === 'Escape') {
            closeModal(modalOverlay);
            document.removeEventListener('keydown', escListener);
        }
    });
}

// Close and remove modal
function closeModal(modal) {
    modal.classList.remove('active');
    setTimeout(() => {
        modal.remove();
    }, 300);
}

// Highlight the current navigation item
function highlightCurrentNav() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.admin-nav a');
    
    navLinks.forEach(link => {
        // Remove any existing active class
        link.classList.remove('active');
        
        // Get the link path
        const linkPath = link.getAttribute('href');
        
        // Check if the current path starts with the link path
        if (currentPath === linkPath || 
            (linkPath !== '/admin' && currentPath.startsWith(linkPath))) {
            link.classList.add('active');
        }
        
        // Special case for the dashboard
        if (linkPath === '/admin' && currentPath === '/admin') {
            link.classList.add('active');
        }
    });
}

// Basic form validation
function initFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            let isValid = true;
            
            // Get all required fields
            const requiredFields = form.querySelectorAll('[required]');
            
            requiredFields.forEach(field => {
                // Remove any existing error messages
                const existingError = field.parentNode.querySelector('.form-error');
                if (existingError) {
                    existingError.remove();
                }
                
                field.classList.remove('is-invalid');
                
                // Check if field is empty
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('is-invalid');
                    
                    // Add error message
                    const errorMsg = document.createElement('div');
                    errorMsg.className = 'form-error';
                    errorMsg.textContent = 'This field is required';
                    field.parentNode.appendChild(errorMsg);
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                // Scroll to first error
                const firstError = form.querySelector('.is-invalid');
                if (firstError) {
                    firstError.focus();
                    firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        });
    });
}

// Setup API interactions for admin actions
function setupApiInteractions() {
    // Example: Add AJAX actions for buttons with data-api-action attribute
    document.body.addEventListener('click', function(e) {
        const target = e.target.closest('[data-api-action]');
        
        if (target) {
            e.preventDefault();
            
            const action = target.getAttribute('data-api-action');
            const endpoint = target.getAttribute('data-api-endpoint');
            const method = target.getAttribute('data-api-method') || 'GET';
            const confirmMsg = target.getAttribute('data-confirm');
            
            const performAction = () => {
                // Show loading state
                target.classList.add('is-loading');
                if (!target.querySelector('.spinner')) {
                    const spinner = document.createElement('span');
                    spinner.className = 'spinner';
                    target.appendChild(spinner);
                }
                
                // Make API request
                fetch(endpoint, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    credentials: 'same-origin'
                })
                .then(response => {
                    // Remove loading state
                    target.classList.remove('is-loading');
                    const spinner = target.querySelector('.spinner');
                    if (spinner) spinner.remove();
                    
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.detail || 'An error occurred');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    showNotification(data.message || 'Action completed successfully', 'success');
                    
                    // Reload or update UI
                    if (target.getAttribute('data-reload') === 'true') {
                        window.location.reload();
                    }
                })
                .catch(error => {
                    showNotification(error.message || 'An error occurred', 'error');
                });
            };
            
            if (confirmMsg) {
                showConfirmationModal(confirmMsg, performAction);
            } else {
                performAction();
            }
        }
    });
}

// Initialize tooltips
function initTooltips() {
    const tooltips = document.querySelectorAll('[data-tooltip]');
    
    tooltips.forEach(element => {
        const tooltipText = element.getAttribute('data-tooltip');
        
        element.addEventListener('mouseenter', function() {
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = tooltipText;
            
            document.body.appendChild(tooltip);
            
            const rect = element.getBoundingClientRect();
            tooltip.style.top = `${rect.top - tooltip.offsetHeight - 10 + window.scrollY}px`;
            tooltip.style.left = `${rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2)}px`;
            
            setTimeout(() => {
                tooltip.classList.add('show');
            }, 10);
            
            element.addEventListener('mouseleave', function onMouseLeave() {
                tooltip.classList.remove('show');
                setTimeout(() => {
                    tooltip.remove();
                }, 200);
                element.removeEventListener('mouseleave', onMouseLeave);
            });
        });
    });
}

// Initialize table sorting
function initTableSorting() {
    const tables = document.querySelectorAll('.admin-table');
    
    tables.forEach(table => {
        const headers = table.querySelectorAll('th');
        
        headers.forEach((header, index) => {
            // Skip headers with 'no-sort' class
            if (header.classList.contains('no-sort')) return;
            
            header.classList.add('sortable');
            header.setAttribute('tabindex', '0');
            
            // Add sort icons
            const sortIcon = document.createElement('span');
            sortIcon.className = 'sort-icon';
            header.appendChild(sortIcon);
            
            header.addEventListener('click', () => {
                // Get current sort direction
                const currentDir = header.getAttribute('data-sort') || 'none';
                const newDir = currentDir === 'asc' ? 'desc' : 'asc';
                
                // Reset all headers
                headers.forEach(h => h.setAttribute('data-sort', 'none'));
                
                // Set new sort direction
                header.setAttribute('data-sort', newDir);
                
                // Sort the table
                sortTable(table, index, newDir);
            });
            
            // Allow keyboard navigation
            header.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    header.click();
                }
            });
        });
    });
}

// Sort table
function sortTable(table, columnIndex, direction) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    // Sort rows
    rows.sort((a, b) => {
        const cellA = a.querySelectorAll('td')[columnIndex];
        const cellB = b.querySelectorAll('td')[columnIndex];
        
        if (!cellA || !cellB) return 0;
        
        const valueA = cellA.textContent.trim();
        const valueB = cellB.textContent.trim();
        
        // Check if values are numeric
        if (!isNaN(valueA) && !isNaN(valueB)) {
            return direction === 'asc' 
                ? Number(valueA) - Number(valueB) 
                : Number(valueB) - Number(valueA);
        }
        
        // Sort as strings
        return direction === 'asc' 
            ? valueA.localeCompare(valueB) 
            : valueB.localeCompare(valueA);
    });
    
    // Re-append rows in new order
    rows.forEach(row => tbody.appendChild(row));
}

// Dashboard animations
function setupDashboardAnimations() {
    // Add entrance animations to dashboard cards
    const cards = document.querySelectorAll('.stat-card, .admin-actions');
    
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
        
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 + (index * 100));
    });
}

// Show notification message
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    
    // Create notification content with icon
    const iconMap = {
        'info': '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>',
        'error': '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>',
        'success': '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>'
    };
    
    const icon = document.createElement('span');
    icon.className = 'notification-icon';
    icon.innerHTML = iconMap[type] || iconMap.info;
    
    const messageSpan = document.createElement('span');
    messageSpan.textContent = message;
    
    notification.appendChild(icon);
    notification.appendChild(messageSpan);
    
    // Create a notifications container if it doesn't exist
    let notificationsContainer = document.querySelector('.notifications-container');
    if (!notificationsContainer) {
        notificationsContainer = document.createElement('div');
        notificationsContainer.className = 'notifications-container';
        document.body.appendChild(notificationsContainer);
    }
    
    notificationsContainer.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('notification-hide');
        setTimeout(() => {
            notification.remove();
            
            // Remove container if empty
            if (notificationsContainer.children.length === 0) {
                notificationsContainer.remove();
            }
        }, 300);
    }, 5000);
}

// HTMX specific handlers
document.addEventListener('htmx:beforeSwap', function(evt) {
    // Handle response status codes
    if (evt.detail.xhr.status >= 400) {
        // Show error message for failed requests
        const errorMsg = evt.detail.xhr.responseText;
        showNotification(errorMsg, 'error');
        evt.detail.shouldSwap = false;
    }
});

document.addEventListener('htmx:responseError', function(evt) {
    showNotification('An error occurred while processing your request.', 'error');
}); 