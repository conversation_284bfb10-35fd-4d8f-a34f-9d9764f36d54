import json
from typing import List

import json_repair
from app.db.models import (
    ArticleProcessStatus,
    ArticleTopicRelation,
    Filter,
    FilterCharacterSetting,
    ProcessStatus,
    SummarizedArticle,
    TopicContent,
)
from app.db.session import Session, get_db_context
from app.schemas.models.filter import Filter as FilterSchema
from app.service.filter.filter import is_wanted_by_filters, translate_en_to_zh
from app.util.llms import same_news
from app.util.redis import get_redis_client
from sqlalchemy import and_, select

from tasks.celery_app import celery_app
from app.logging import get_logger

logger = get_logger()

FILTER_DETAILS_KEY = "filter:{filter_id}:details"  # Hash of filter details


@celery_app.task(bind=True)
def classify_summarized_article_for_topic(self, article_id: int, fc_id: int):
    with get_db_context() as session:
        if get_process_lock(session, article_id, fc_id):
            try:
                _classify_summarized_article_for_topic(
                    session,
                    article_id,
                    fc_id,
                )
                mark_process_success(session, article_id,fc_id)
            except Exception as e:
                session.rollback()
                mark_process_failed(session, article_id,fc_id)
                logger.error(f"分类文章失败: {str(e)}")
                raise


def _classify_summarized_article_for_topic(
    db: Session,
    article_id: int,
    fc_id: int,
    THRESHOLD: float = 0.7,
    MIN_PROB: float = 0.5,
):
    article = db.get(SummarizedArticle, article_id)
    if article is None:
        logger.error(f"Article with id {article_id} not found.")
        raise ValueError(f"Article with id {article_id} not found.")

    logger.info(
        f"Classify summarized article {article_id} for filter_character {fc_id}"
    )
    stmt = (
        select(
            TopicContent,
            TopicContent.embedding_vector.l2_distance(article.embedding_vector),
        )
        .where(
            and_(
                TopicContent.filter_character_id == fc_id,
                TopicContent.embedding_vector.l2_distance(
                    article.embedding_vector
                )
                < THRESHOLD,
            )
        )
        .order_by(
            TopicContent.embedding_vector.l2_distance(
                article.embedding_vector
            ).desc()
        )
        .limit(1)
    )
    result = db.execute(stmt)
    res = result.first()
    if res is None:
        logger.info(f"Create new topic for: {article.titles[0]}")
        insert_new_topic(db, article, fc_id)
        return

    topic, distance = res
    logger.info(f"Found topic {topic.id} with distance {distance}")
    same_prob = same_news(article.summary, topic.summary)
    answer = json_repair.loads(same_prob.answer)
    assert answer is not None
    assert type(answer) is dict
    if not hasattr(answer, "keys") or "value" not in answer.keys():
        logger.warning(
            f"Error getting same news probability for article {article.id} and {topic.id}"
        )
        return
    prob = answer["value"]
    if prob < MIN_PROB:
        logger.info(f"Article {article.id} and {topic.id} is not same news, create new topic")
        if len(article.titles) == 0:
            article.titles.append("")
        insert_new_topic(db, article, fc_id)
        return

    logger.info(f"Article {article.id} is topic {topic.id} with distance {distance}")
    rel = ArticleTopicRelation(article_id=article.id, topic_id=topic.id)
    db.add(rel)
    # TODO: update the summary and embedding vector
    return


def insert_new_topic(db: Session, article: SummarizedArticle, fc_id: int):
    topic = TopicContent(
        filter_character_id=fc_id,
        summary=article.summary,
        summary_zh=translate_en_to_zh(article.summary),
        embedding_vector=article.embedding_vector,
        title=article.titles[0],
        ts=article.ts,
    )

    db.add(topic)
    db.flush()
    rel = ArticleTopicRelation(article_id=article.id, topic_id=topic.id)
    db.add(rel)
    logger.info(f"Classified article {article.id} {article.titles} to topic {topic.id}")
    filters = get_filter_details(fc_id, db)
    if filters is None:
        logger.info(f"no filters for {fc_id}")
        return

    need_details = False
    for filter in filters:
        if filter.need_details:
            need_details = True
            break

    if is_wanted_by_filters(topic, filters, fc_id):
        topic.need_details = need_details
        topic.wanted = True
        db.add(topic)


def get_filter_details(fc_id: int, session: Session) -> List[FilterSchema] | None:
    """Get filter details for a given filter character ID with Redis caching.

    Args:
        fc_id: Filter character setting ID

    Returns:
        List of Filter objects or None if not found

    Raises:
        ValueError: If fc_id is invalid
    """
    if not fc_id or fc_id < 1:
        raise ValueError("Invalid filter character ID")

    redis_client = get_redis_client()
    cache_key = f"{FILTER_DETAILS_KEY.format(filter_id=fc_id)}"

    try:
        # Try to get from cache first
        cached_data = redis_client.get(cache_key)
        if cached_data:
            filter_data = json_repair.loads(cached_data)
            logger.debug(f"Cache hit for filter_details:{fc_id}")
            return [FilterSchema(**f) for f in filter_data]

        # Cache miss, load from database
        filter_character = session.get(FilterCharacterSetting, fc_id)
        if not filter_character:
            logger.warning(f"FilterCharacterSetting {fc_id} not found")
            return None

        # Batch load all filters in one query
        filters = (
            session.query(Filter).filter(Filter.id.in_(filter_character.filters)).all()
        )

        if not filters:
            return None

        # Cache the results
        filter_data = [
            {
                "id": f.id,
                "name": f.name,
                "filter_type": f.filter_type.value,  # Enum value
                "prompt": f.prompt,
                "need_details": f.need_details,
                "model": f.model,
                "api_base": f.api_base,
                "api_key": f.api_key,
            }
            for f in filters
        ]

        redis_client.setex(
            cache_key,
            3600,  # Cache for 1 hour
            json.dumps(filter_data),
        )

        logger.debug(f"Cached filter_details:{fc_id}")
        return filters

    except Exception as e:
        logger.error(f"Error getting filter details for {fc_id}: {str(e)}")
        raise


def mark_process_success(db: Session, article_id: int, fc_id: int):
    db.query(ArticleProcessStatus).filter(
        and_(
            ArticleProcessStatus.article_id == article_id,
            ArticleProcessStatus.status == ProcessStatus.processing,
            ArticleProcessStatus.topic_id == fc_id,
        )
    ).update({ArticleProcessStatus.status: ProcessStatus.success})


def mark_process_failed(db: Session, article_id: int, fc_id: int):
    db.query(ArticleProcessStatus).filter(
        and_(
            ArticleProcessStatus.article_id == article_id,
            ArticleProcessStatus.status == ProcessStatus.processing,
            ArticleProcessStatus.topic_id == fc_id,
        )
    ).update({ArticleProcessStatus.status: ProcessStatus.failed})


def get_process_lock(db: Session, article_id: int, fc_id: int) -> bool:
    updated = (
        db.query(ArticleProcessStatus)
        .filter(
            and_(
                ArticleProcessStatus.article_id == article_id,
                ArticleProcessStatus.status == ProcessStatus.pending,
                ArticleProcessStatus.topic_id == fc_id,
            )
        )
        .update({ArticleProcessStatus.status: ProcessStatus.processing})
    )
    if updated != 1:
        logger.warning(f"update {updated}, article {article_id} not in pending status")
        return False
    return True
