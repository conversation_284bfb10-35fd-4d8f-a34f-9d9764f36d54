from json_repair import json_repair as json

from app.logging import get_logger
from app.util.llms import same_news

logger = get_logger()

def same_news_prob(t1: str, t2: str) -> float | None:
    try:
        response = same_news(t1, t2)
        answer = json.loads(response.answer)
        return answer["value"]
    except Exception as e:
        logger.error(f"Error getting same news prob: {str(e)}")
        return None
