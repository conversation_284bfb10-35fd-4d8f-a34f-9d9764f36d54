import re
from app.db.session import get_db_context
from app.logging import get_logger
import json_repair as json

from app.db.models import (
    CompleteArticle,
    SummarizedArticle,
    TopicContent,
    ArticleTopicRelation,
    ArticleProcessStatus,
    ProcessStatus,
)
from app.util.llms import same_news, structure_summarize, vectorize
from tasks.celery_app import celery_app
from tasks.topic.topic_router_tasks import distribute_article

logger = get_logger()


""" TODO
- revectorize when add a new topic with similarity < threshold
"""


@celery_app.task(bind=True)
def summarize_complete_article(self, article_id: int):
    try:
        _summarize_complete_article(article_id)
    except ValueError as e:
        logger.error(f"文章反序列化失败: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"总结文章失败: {str(e)}")
        raise
    pass


def _summarize_complete_article(article_id: int):
    with get_db_context() as db:
        db.query(CompleteArticle).filter(CompleteArticle.id == article_id).update(
            {CompleteArticle.summarized: True}
        )

        article = db.get(CompleteArticle, article_id)
        assert article is not None
        logger.info(f"Summarizing article {article.id} {article.title}")
        content = re.sub(" +", " ", article.content)
        space_count = len(article.content) - len(content)
        logger.info(
            f"Reduced {space_count} spaces in article {article.id}, total length {len(article.content)}"
        )
        if len(article.content) > 40000:
            logger.info(
                f"Article {article.id} {article.title} is too long to summarize"
            )
            return
        res = structure_summarize(article.content)
        answer = res.answer
        structure_summary = json.loads(answer)
        if (
            structure_summary is None
            or not hasattr(structure_summary, "keys")
            or "summary" not in structure_summary.keys()
        ):
            logger.error(f"Error getting summary for article {article.id}")
            return
        summarized_article = SummarizedArticle(
            id=article.id,
            source_id=article.source_id,
            summary=structure_summary["summary"],
            titles=structure_summary["titles"],
            tags=structure_summary["tags"],
            entities=structure_summary["entities"],
            short_summaries=structure_summary["short_summaries"],
            style=structure_summary["style"],
            arguments=structure_summary["arguments"],
            ts=article.ts,
        )
        db.add(summarized_article)
        db.flush()
        logger.info(f"Summarized article {article.id} {article.title}")
        vectorize_summarized_complete_article.delay(summarized_article.id)


# NOTE: split summary and vectoring, because I want to leverage the batch api
@celery_app.task(bind=True)
def vectorize_summarized_complete_article(self, article_id: int):
    try:
        _vectorize_summarized_complete_article(article_id)
    except ValueError as e:
        logger.error(f"文章反序列化失败: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"总结文章失败: {str(e)}")
        raise
    pass


def _vectorize_summarized_complete_article(article_id: int):
    with get_db_context() as session:
        article = session.get(SummarizedArticle, article_id)
        assert article is not None
        logger.info(f"Vectorizing article {article_id} {article.titles}")
        res = vectorize(article.summary)
        session.query(SummarizedArticle).filter(
            SummarizedArticle.id == article_id
        ).update({SummarizedArticle.embedding_vector: res})
        distribute_article.delay(article_id, article.source_id)
