#!/usr/bin/env python3
"""
Celery task to add Chinese summaries to existing TopicContent records.
"""

import os
import sys

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.db.models import TopicContent
from app.db.session import get_db_context
from app.service.filter.filter import translate_en_to_zh
from app.logging import get_logger
from sqlalchemy import select,and_

from tasks.celery_app import celery_app

logger = get_logger()

@celery_app.task(bind=True)
def translate_all_summaries(self):
    """
    Celery task to add Chinese summaries to all existing TopicContent records that don't have one.
    """
    with get_db_context() as session:
        # Find all topics without Chinese summaries
        stmt = select(TopicContent).where(and_(TopicContent.summary_zh.is_(None),TopicContent.wanted==True))
        topics = session.execute(stmt).scalars().all()
        
        if not topics:
            logger.info("No topics found without Chinese summaries.")
            return "No topics found without Chinese summaries."
        
        logger.info(f"Found {len(topics)} topics without Chinese summaries. Starting translation...")
        
        # Process topics in batches to avoid potential memory issues
        batch_size = 100
        total_updated = 0
        
        for i in range(0, len(topics), batch_size):
            batch = topics[i:i+batch_size]
            
            for topic in batch:
                try:
                    # Translate the English summary to Chinese
                    topic.summary_zh = translate_en_to_zh(topic.summary)
                    logger.debug(f"Translated topic {topic.id}: {topic.title}")
                    total_updated += 1
                except Exception as e:
                    logger.error(f"Error translating topic {topic.id}: {str(e)}")
            
            # Commit the batch
            try:
                session.commit()
                logger.info(f"Committed batch of {len(batch)} translations. Progress: {total_updated}/{len(topics)}")
            except Exception as e:
                session.rollback()
                logger.error(f"Error committing batch: {str(e)}")
        
        logger.info(f"Translation completed. Updated {total_updated} topics with Chinese summaries.")
        return f"Translation completed. Updated {total_updated} topics with Chinese summaries."
