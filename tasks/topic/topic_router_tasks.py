# -*- coding: utf-8 -*-
# 将 vectorize 后的source文章，根据 topics 和 user_source 中的记录，分发任务到不同的worker
# 分发者将文章分发到多个 topic，每个 topic 内进行相似文章聚类和过滤操作。
# 为了防止频繁查询数据库，topic 和 source_provider 的关系需要进行缓存。
# TODO: 如何记录 topic 处理 source_provider 文章的进度？需要考虑到文章的 id 未来都会使用 uuid 而不是自增主键
# 用 GIN 索引，为 filter 的 sources 数组元素建立索引。

from sqlalchemy.exc import IntegrityError  # Import SQLAlchemy's IntegrityError
from typing import List
from psycopg.errors import UniqueViolation
from tasks.celery_app import celery_app
from app.db.models import (
    FilterCharacterSetting,
    ArticleProcessStatus,
)
from tasks.classify.classify_tasks import classify_summarized_article_for_topic
from app.db.session import get_db_context, Session
from app.util.redis import get_redis_client
from app.logging import get_logger

# Redis key patterns
SOURCE_FILTERS_KEY = "source:{source_id}:filters"  # Set of filter IDs
FILTER_SOURCES_KEY = "filter:{filter_id}:sources"  # Set of source IDs

logger = get_logger()


# def update_source_filter_cache(session, filter_setting: FilterCharacterSetting) -> None:
#     """
#     Update Redis cache for a filter setting only if related caches exist.
#     This ensures we only maintain cache for actively used sources/filters.
#     """
#     redis = get_redis_client()
#     filter_key = FILTER_SOURCES_KEY.format(filter_id=filter_setting.id)

#     # Check if this filter has any cache entries
#     has_cache = False
#     if redis.exists(filter_key):
#         has_cache = True
#     else:
#         # Check if any of the source->filter mappings exist
#         for source_id in filter_setting.source_ids:
#             if redis.exists(SOURCE_FILTERS_KEY.format(source_id=source_id)):
#                 has_cache = True
#                 break

#     if not has_cache:
#         return

#     # Update cache since it exists
#     old_source_ids = redis.smembers(filter_key)
#     old_source_ids = {int(sid) for sid in old_source_ids} if old_source_ids else set()
#     new_source_ids = set(filter_setting.source_ids)

#     # Remove filter from sources that are no longer associated
#     for source_id in old_source_ids - new_source_ids:
#         redis.srem(SOURCE_FILTERS_KEY.format(source_id=source_id), filter_setting.id)

#     # Add filter to new sources
#     for source_id in new_source_ids:
#         source_key = SOURCE_FILTERS_KEY.format(source_id=source_id)
#         if redis.exists(source_key):  # Only update if source cache exists
#             redis.sadd(source_key, filter_setting.id)

#     # Update filter's source set
#     redis.delete(filter_key)
#     if new_source_ids:
#         redis.sadd(filter_key, *new_source_ids)

#     # Update filter details if they're cached
#     details_key = FILTER_DETAILS_KEY.format(filter_id=filter_setting.id)
#     if redis.exists(details_key):
#         redis.hmset(details_key, filter_setting)


def get_source_topics(id: int, session: Session) -> List[FilterCharacterSetting]:
    """Get all filter settings that contain the given source_id"""
    redis = get_redis_client()
    source_key = SOURCE_FILTERS_KEY.format(source_id=id)
    filter_ids = redis.smembers(source_key)

    if not filter_ids:
        # Cache miss, load from database and create cache
        filters = session.query(FilterCharacterSetting).filter(FilterCharacterSetting.source_ids.contains([id])).all()
        # Create new cache entry
        if filters:
            for filter_setting in filters:
                redis.sadd(source_key, filter_setting.id)
                # Also update the reverse mapping
                filter_key = FILTER_SOURCES_KEY.format(filter_id=filter_setting.id)
                redis.sadd(filter_key, id)

                logger.info(f"Updated cache for source {id} with filter {filter_setting.id}")
        return filters

    # Cache hit
    filters =session.query(FilterCharacterSetting).filter(
        FilterCharacterSetting.id.in_([int(fid) for fid in filter_ids])).all()
    return filters


@celery_app.task(bind=True)
def distribute_article(self, article_id: int, source_id: int):
    with get_db_context() as db:
        try:
            _distribute_article(article_id, source_id, db)
        except Exception as e:
            db.rollback()
            logger.error(f"分发文章到fc失败: {str(e)}")
            raise


def _distribute_article(article_id: int, source_id: int, session: Session):
    """
    Distribute an article to all topics that are associated with its source.
    """
    filter_characters = get_source_topics(source_id, session)
    fcs = []
    for fc in filter_characters:
        try:
            todo = ArticleProcessStatus(
                source_id=source_id,
                article_id=article_id,
                topic_id=fc.id,
            )
            session.add(todo)
            session.flush()
        except IntegrityError as e:  # Catch SQLAlchemy's IntegrityError
            # Check if the original error is a psycopg UniqueViolation
            if isinstance(getattr(e, "orig", None), UniqueViolation):
                session.rollback()
                logger.info(f"skip process task for article {article_id} in {fc.id} already exists")
            else:
                # Re-raise unexpected IntegrityErrors
                logger.info(f"add article process task failed. article: {article_id} fc: {fc.id} err: {e}")
                raise e
        except Exception as e:
            logger.info(f"add article process task failed. article: {article_id} fc: {fc.id} err: {e}")
            raise e
        fcs.append(fc.id)
    for fc_id in fcs:
        classify_summarized_article_for_topic.delay(article_id, fc_id)
    logger.info(f"分发文章 {article_id} 到 {len(fcs)} 个fc")
