from gevent import monkey
monkey.patch_all()
import importlib
import os
import pkgutil
from celery import Celery,Task
from app.logging import get_logger
from app.middlewares import request_id_var,log_file_var

# replace std to async
# Set Celery-specific log level from environment or use app's LOG_LEVEL
CELERY_LOG_LEVEL = os.getenv("CELERY_LOG_LEVEL", os.getenv("LOG_LEVEL", "INFO"))
logger = get_logger()

class ContextVarTask(Task):
    """Celery Task that sets/resets a ContextVar for logid."""

    def __call__(self, *args, **kwargs):
        # Generate a unique logid (e.g., using the Celery task ID)
        current_task_id = self.request.id  # Unique ID for the task
        token = request_id_var.set(current_task_id)  # Bind logid to the current context
        token2 = log_file_var.set("celery")
        
        try:
            # Execute the task
            return super().__call__(*args, **kwargs)
        finally:
            # Reset the context variable after task completion
            request_id_var.reset(token)
            log_file_var.reset(token2)

def auto_include_modules(package_name):
    """自动发现指定包下的所有模块"""
    modules = []
    package = importlib.import_module(package_name)
    for _, name, is_pkg in pkgutil.walk_packages(package.__path__, f"{package.__name__}."):
        if not is_pkg:  # 仅包含模块，排除子包
            modules.append(name)
    return modules

# Initialize Celery app with RabbitMQ broker and PostgreSQL backend
celery_app = Celery(
    "celery_app",
    broker="amqp://admin:password123@localhost:5672/eyesaver",
    backend="db+postgresql://liam@127.0.0.1:5432/liam",
    include=auto_include_modules("tasks"),
)
celery_app.Task = ContextVarTask
celery_app.autodiscover_tasks(["tasks"])

# Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    acks_late=True,
    task_reject_on_worker_lost=True,
    enable_utc=True,
    task_track_started=True,
    task_time_limit=300,  # 5 minutes max per task
    worker_prefetch_multiplier=10,  # Process one task at a time
    beat_schedule={
        "sync-sources-every-5-minute": {
            "task": "keep_sync_sources",
            "schedule": 300.0,  # 每60秒执行一次
        }
    },
)
