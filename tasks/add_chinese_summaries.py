#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add Chinese summaries to existing TopicContent records.
This is a one-time script to update existing records after adding the summary_zh field.
"""

import os
import sys
import asyncio

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.db.models import TopicContent
from app.db.session import get_db_context
from app.service.filter.filter import translate_en_to_zh_async
from app.logging import get_logger
from sqlalchemy import select,and_

logger = get_logger()

async def translate_topic_summary(session, topic):
    try:
        # Translate the English summary to Chinese
        topic.summary_zh = await translate_en_to_zh_async(topic.summary)
        logger.debug(f"Translated topic {topic.id}: {topic.title}")
        return True
    except Exception as e:
        logger.error(f"Error translating topic {topic.id}: {str(e)}")
        return False

async def add_chinese_summaries():
    """
    Add Chinese summaries to all existing TopicContent records that don't have one.
    """
    with get_db_context() as session:
        # Find all topics without Chinese summaries
        stmt = select(TopicContent).where(and_(TopicContent.summary_zh.is_(None),TopicContent.wanted==True))
        topics = session.execute(stmt).scalars().all()
        
        if not topics:
            logger.info("No topics found without Chinese summaries.")
            return
        
        logger.info(f"Found {len(topics)} topics without Chinese summaries. Starting translation...")
        
        # Process topics in batches to avoid potential memory issues
        batch_size = 100
        total_updated = 0
        
        for i in range(0, len(topics), batch_size):
            batch = topics[i:i+batch_size]
            
            # Translate topics concurrently
            translation_results = await asyncio.gather(
                *[translate_topic_summary(session, topic) for topic in batch]
            )
            
            # Count successful translations
            total_updated += sum(1 for result in translation_results if result)
            
            # Commit the batch
            try:
                session.commit()
                logger.info(f"Committed batch of {len(batch)} translations. Progress: {total_updated}/{len(topics)}")
            except Exception as e:
                session.rollback()
                logger.error(f"Error committing batch: {str(e)}")
        
        logger.info(f"Translation completed. Updated {total_updated} topics with Chinese summaries.")

if __name__ == "__main__":
    logger.info("Starting Chinese summary translation process...")
    asyncio.run(add_chinese_summaries())
    logger.info("Chinese summary translation process completed.")
