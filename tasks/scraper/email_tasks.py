from datetime import datetime, timedelta, timezone
import time
import contextlib

import mailparser
import trafilatura
import imaplib
from fastapi import HTT<PERSON>Exception
from sqlalchemy.orm import Session

from app.db.models import (
    CompleteArticle,
    EmailSource,
    EmailSourceFailedToExtract,
    IncompleteArticle,
    SourceProvider,
    SourceProviderType,
)
from app.db.session import get_db_context
from app.service.scraper.email.llm import parse_classify_result
from app.util.llms import extract_newsletter
from app.util.shorten_url import process_html
from tasks.celery_app import celery_app
from tasks.classify.summarize_tasks import summarize_complete_article
from app.logging import get_logger
from tasks.scraper.blurb_tasks import fetch_incomplete_article

logger = get_logger()

@celery_app.task(bind=True)
def extract_email_content(self, email_id: int):
    with get_db_context() as session:
        try:
            with get_db_context() as session:
                email = session.get(EmailSource, email_id)
                assert email is not None
                session.query(EmailSource).filter(EmailSource.id == email_id).update(
                    {EmailSource.extracted: True}
                )
                _extract_email_content(email)
        except ValueError as e:
            logger.error(f"数据源反序列化失败: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"邮件内容抽取失败: {str(e)}")
            raise
    pass


def _extract_email_content(email: EmailSource):
    with get_db_context() as session:
        content_md = convert_html_to_md(email.content_html)
        if content_md is None:
            logger.warning(f"Failed to convert md of email {email.id}")
            failed_email = EmailSourceFailedToExtract(
                id=email.id, source_id=email.source_id
            )
            session.merge(failed_email)
            return

        extract_result = extract_newsletter(content_md)
        if extract_result is None:
            # indicate api error
            logger.warning(f"Failed to extract newsletter of email {email.id}")
            return

        extracted = parse_classify_result(extract_result)

        logger.info(f"Extracted newsletter of email {email.id}, result {extracted}")

        if not extracted.is_newsletter:
            new_article = CompleteArticle(
                content=content_md,
                source_id=email.source_id,
                origin_id=email.id,
                title=email.subject,
                summarized=False,
                ts=email.ts,
            )
            session.add(new_article)
            session.flush()
            summarize_complete_article.delay(new_article.id)
            return

        articles_to_fetch = []
        for article in extracted.articles:
            incomplete_article = IncompleteArticle(
                depth=1,
                headline=article.headline,
                blurb=article.blurb,
                link=article.link,
                source_id=email.source_id,
                origin_id=email.id,
                expanded=False,
                ts=email.ts,
            )
            session.add(incomplete_article)
            session.flush()
            articles_to_fetch.append(incomplete_article)

        for article in articles_to_fetch:
            fetch_incomplete_article.delay(article.id)


def convert_html_to_md(html: str) -> str | None:
    return trafilatura.extract(
        html, favor_precision=True, deduplicate=True, include_links=True
    )


@contextlib.contextmanager
def imap_client(provider: SourceProvider):
    """Context manager for IMAP client connections."""
    if provider.source_type != SourceProviderType.email:
        raise HTTPException(status_code=400, detail="Provider type must be email")
    
    client = None
    try:
        logger.info(
            f"Connecting to mailbox {provider.source_type} {provider.imap_server} {provider.imap_port} {provider.user} {provider.password}"
        )
        client = imaplib.IMAP4_SSL(provider.imap_server.__str__())
        client.login(provider.user.__str__(), provider.password.__str__())
        yield client
    except Exception as e:
        logger.error(f"Error connecting to mailbox: {e}")
        raise
    finally:
        if client:
            try:
                client.logout()
            except Exception as e:
                logger.error(f"Error logging out from mailbox: {e}")


def sync_mailbox(provider: SourceProvider):
    if provider.source_type != SourceProviderType.email:
        raise HTTPException(status_code=399, detail="Provider type must be email")
    logger.info(f"Syncing mailbox provider {provider}")
    
    duration = timedelta(days=29)
    
    with get_db_context() as session:
        with imap_client(provider) as client:
            save_new_emails_to_db(session, client, duration, provider.id)


def save_email_to_db(session: Session, email: tuple, source_id: int):
    if isinstance(email, tuple):
        uid = int(email[0].decode("utf-8").split()[0])
    else:
        logger.info(f"Email {email} is not a tuple")
        return

    # Check if email already exists in the database
    existing_email = session.get(EmailSource, uid)
    if existing_email is not None:
        logger.info(f"Email {uid} already exists in the database")
        return

    parsed = mailparser.parse_from_bytes(email[1])

    content_html = []
    for line in parsed.text_html:
        processed = process_html(line, session)
        if processed is None or len(processed) < 10:
            continue
        content_html.append(processed)

    # Create new MailBox object
    new_email = EmailSource(
        id=uid,
        source_id=source_id,
        subject=parsed.subject,
        sender=parsed._from[0][0],
        sender_address=parsed._from[0][1],
        ts=int(parsed.date.timestamp()),  # Convert to UTC int timestamp
        content_html="\n".join(content_html),
        extracted=False,
    )
    session.add(new_email)
    session.flush()
    extract_email_content.delay(new_email.id)
    logger.info(f"Saved email {uid}:{new_email.subject} to db")




# fetch emails after earliest_timestamp and save unfetched emails to db
def save_new_emails_to_db(
    session: Session,
    client: imaplib.IMAP4_SSL,
    duration: timedelta,
    source_id: int,
):
    logger.info(f"Saving new emails to db for provider {source_id}")
    typ, _ = client.select("INBOX")
    if typ != "OK":
        logger.error(f"Error selecting INBOX: {_}")
        raise Exception(f"Error selecting INBOX: {_}")

    end_date = datetime.now(timezone.utc)
    start_date = end_date - duration

    # Search for emails within the specified duration
    date_criterion = f'(SINCE "{start_date.strftime("%d-%b-%Y")}" BEFORE "{end_date.strftime("%d-%b-%Y")}")'
    # UNSEEN criterion
    unseen_criterion = "UNSEEN"
    typ, data = client.search(f"{date_criterion} {unseen_criterion}")
    if typ != "OK":
        logger.error(f"Error searching for emails: {data}")
        raise Exception(f"Error searching for emails: {data}")

    # get the email numbers
    uids = data[0].decode().split()
    logger.info(f"{len(uids)} messages to fetch from {start_date}")

    for uid in uids:
        logger.info(f"Fetching email {uid} ")
        typ, data = client.fetch(uid, "(UID RFC822)")
        if typ != "OK":
            logger.error(f"Error fetching email: {data}")
            continue
        if not data:
            logger.error(f"No data found for email: {uid}")
            continue

        save_email_to_db(session=session, email=data[0], source_id=source_id)
        session.commit()
        typ, data = client.store(uid, "+FLAGS", "\\Seen")
        if typ != "OK":
            logger.error(f"Error marking email as seen: {data}")
            continue

