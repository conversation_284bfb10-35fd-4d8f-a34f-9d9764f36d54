from datetime import datetime
from typing import List, Optional

import feedparser
from fastapi import HTT<PERSON>Exception
from sqlalchemy import and_, select
from sqlalchemy.orm import Session

from app.db.models import CompleteArticle, RssSource, SourceProvider, SourceProviderType
from app.db.session import get_db_context
from app.service.web_crawler import general_crawl
from tasks.celery_app import celery_app
from tasks.classify.summarize_tasks import summarize_complete_article
from app.logging import get_logger

logger = get_logger()


@celery_app.task(bind=True)
def extract_rss(self, rss_id: int):
    try:
        with get_db_context() as session:
            rss = session.get(RssSource, rss_id)
            assert rss is not None
            session.query(RssSource).filter(
                and_(RssSource.id == rss_id, RssSource.extracted == False)
            ).update({RssSource.extracted: True})
            article_id = _extract_rss(rss, session)
        if article_id > 0:
            logger.info(f"Rss {article_id} extracted Successfully")
            summarize_complete_article.delay(article_id)
    except Exception as e:
        logger.error(f"rss[{rss_id}]内容抽取失败: {str(e)}")
        raise
    pass


def _extract_rss(rss: RssSource, session: Session) -> int:
    # 抓取文章内容
    content_md = general_crawl(rss.link)
    if not content_md:
        logger.warning(f"Failed to extract content from {rss.link}")
        # 标记为已处理，避免重复处理失败的条目
        return 0

    # 保存为完整文章
    new_article = CompleteArticle(
        source_id=rss.source_id,
        origin_url=rss.link,
        origin_id=rss.id,
        content=content_md,
        title=rss.title,
        summarized=False,
        ts=rss.ts,
    )

    session.add(new_article)
    session.flush()
    logger.info(f"Successfully extract {rss.title} to article {new_article.id}")
    return new_article.id


def sync_rss(provider: SourceProvider):
    """
    同步指定RSS源的内容到数据库

    Args:
        provider (SourceProvider): RSS提供者对象
        session (Session): 数据库会话
    """
    if provider.source_type != SourceProviderType.rss:
        raise HTTPException(status_code=400, detail="Provider type must be rss")

    if not provider.url:
        raise HTTPException(status_code=400, detail="RSS URL is required")

    logger.info(f"Syncing RSS provider {provider.nickname} from {provider.url}")

    entries = fetch_rss_entries(provider.url)
    if not entries:
        logger.error(f"Failed to fetch RSS from {provider.url}")
        return
    with get_db_context() as session:
        ids = []
        for entry in entries:
            # 检查是否已存在
            stmt = select(RssSource).where(RssSource.link == entry["link"])
            existing = session.execute(stmt).scalar_one_or_none()

            if existing:
                continue

            try:
                # 时间字段处理
                raw_ts = entry["updated"] or entry["published"]
                ts = None
                if raw_ts:
                    if isinstance(raw_ts, datetime):
                        # 已经是datetime对象，直接转换为时间戳
                        ts = int(raw_ts.timestamp())
                    else:
                        try:
                            # 尝试解析ISO格式时间字符串
                            ts = int(
                                datetime.fromisoformat(
                                    str(raw_ts).replace("Z", "+00:00")
                                ).timestamp()
                            )
                        except ValueError:
                            try:
                                # 尝试解析时间戳
                                ts_str = str(raw_ts)
                                if len(ts_str) == 13:  # 13位时间戳
                                    ts = int(int(ts_str) / 1000)
                                elif len(ts_str) == 10:  # 10位时间戳
                                    ts = int(ts_str)
                            except (ValueError, TypeError):
                                # 如果都失败了，使用当前时间
                                ts = int(datetime.now().timestamp())

                # 创建新的RSS源记录
                rss_source = RssSource(
                    entry_id=str(entry["id"]),  # entry_id必须是字符串
                    link=str(entry["link"]),  # link必须是字符串且唯一
                    title=str(entry["title"] or ""),  # title不能为None
                    source_id=int(provider.id),  # 确保source_id是整数
                    extracted=False,  # 布尔值
                    ts=ts,  # 处理后的10位时间戳
                )
                session.add(rss_source)
                session.flush()
                ids.append(rss_source.id)
                logger.info(f"Added new RSS entry: {entry['title']}")
            except Exception as e:
                session.rollback()
                logger.error(f"Error saving RSS entry: {e}")
        # 提交所有新增的RSS源记录
        session.commit()
        logger.info(f"Successfully synced {len(ids)} RSS entries")
        for id in ids:
            extract_rss.delay(id)


def fetch_rss_entries(url: str) -> Optional[List[dict]]:
    """
    获取RSS源的所有条目
    支持RSS和Atom格式

    Args:
        url (str): RSS源的URL

    Returns:
        List[dict]: RSS条目列表
    """
    try:
        # 使用 requests 替代 aiohttp
        from requests import get
        response = get(url)
        if response.status_code != 200:
            logger.error(
                f"Failed to fetch RSS from {url}, status: {response.status_code}"
            )
            return None

        content = response.text
        feed = feedparser.parse(content)

        if feed.bozo:  # 检查解析是否有错误
            logger.error(
                f"Error parsing RSS feed from {url}: {feed.bozo_exception}"
            )
            return None

        entries = []
        for entry in feed.entries:
            # 尝试从多个可能的字段中获取ID
            entry_id = entry.get("id", entry.get("guid", entry.get("link")))

            # 处理发布时间和更新时间
            published = entry.get("published_parsed") or entry.get(
                "pubDate_parsed"
            )
            updated = entry.get("updated_parsed") or published

            entries.append(
                {
                    "id": entry_id,
                    "link": entry.get("link", ""),
                    "title": entry.get("title", ""),
                    "updated": datetime(*updated[:6]) if updated else None,
                    "published": datetime(*published[:6])
                    if published
                    else None,
                }
            )

        return entries

    except Exception as e:
        logger.error(f"Error fetching RSS from {url}: {e}")
        return None
