import time
from typing import Dict, Callable
from redis import Redis

from app.db.models.source import SourceProviderType, SourceSubProviderType
from app.logging import get_logger
from app.schemas.models.source import SourceProvider as SourceProviderSchema
from app.util.redis import RedisDistributedLock

# 任务超时阈值（秒）
TASK_TIMEOUT_THRESHOLD = 600  # 10分钟

logger = get_logger()

class CrawlerManager:
    """爬虫任务管理器"""

    def __init__(self, redis_client: Redis, crawler_map: Dict[str, Callable]):
        """
        初始化爬虫管理器

        Args:
            redis_client: Redis客户端实例
            crawler_map: 爬虫类型映射字典
        """
        self.redis = redis_client
        self.crawler_map = crawler_map
        self._cleanup_legacy_locks()

    def _cleanup_legacy_locks(self):
        """清理遗留的锁"""
        RedisDistributedLock.cleanup_locks(self.redis)

    def execute_task(self, source: SourceProviderSchema):
        """
        执行爬虫任务

        Args:
            source: 数据源对象
        """
        logger.info(f"开始执行爬虫任务: {source.nickname}")
        lock_key = f"crawler_lock:{source.id}"
        task_start_key = f"crawler_start_time:{source.id}"

        # 检查上次任务开始时间
        last_start_time = self.redis.get(task_start_key)
        if last_start_time:
            last_start = float(last_start_time)
            current_time = time.time()
            if current_time - last_start > TASK_TIMEOUT_THRESHOLD:
                logger.error(
                    f"数据源 {source.nickname} (ID: {source.id}) 的上次任务可能已经超时 "
                    f"(开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(last_start))})"
                )
        try:
            with RedisDistributedLock(self.redis, lock_key) as lock:
                if lock.acquired:
                    start_time = time.monotonic()
                    current_time = time.time()
                    # 记录任务开始时间
                    self.redis.set(task_start_key, str(current_time))
                    logger.info(f"开始处理数据源: {source.nickname} (ID: {source.id})")

                    crawler = self.crawler_map.get(
                        self._get_crawler_type_key(
                            source.source_type, source.source_sub_type
                        )
                    )
                    if not crawler:
                        logger.error(
                            f"未知的数据源类型: {source.source_type}_{source.source_sub_type}"
                        )
                        return

                    try:
                        crawler(source)
                        elapsed = time.monotonic() - start_time
                        logger.info(
                            f"数据源 {source.nickname} 处理完成，耗时: {elapsed:.2f}秒"
                        )
                    except Exception as e:
                        logger.error(
                            f"处理数据源 {source.nickname} 时发生错误: {str(e)}",
                            exc_info=True,
                        )
                else:
                    logger.warning(
                        f"数据源 {source.nickname} 已有任务在运行中，跳过本次执行"
                    )
        except Exception as e:
            logger.error(f"任务执行过程发生异常: {str(e)}", exc_info=True)

    @staticmethod
    def _get_crawler_type_key(source_type: SourceProviderType, source_sub_type: SourceSubProviderType) -> str:
        """
        获取爬虫类型键名

        Args:
            source_type: 数据源类型
            source_sub_type: 数据源子类型

        Returns:
            str: 爬虫类型键名
        """
        return f"{source_type}_{source_sub_type}"
