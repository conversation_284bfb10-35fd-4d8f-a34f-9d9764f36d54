from tasks.celery_app import celery_app
from app.db.session import get_db_context
from app.db.models import SourceProvider
from app.schemas.models.source import SourceProvider as SourceProviderSchema
from app.logging import get_logger
from sqlalchemy import select
from app.util.redis import get_redis_client
from tasks.scraper.hacker_news_tasks import sync_hacker_news
from tasks.scraper.rss_tasks import sync_rss
from tasks.scraper.email_tasks import sync_mailbox
from tasks.scraper.crawler_manager import CrawlerManager
from app.db.models import SourceProviderType, SourceSubProviderType

logger = get_logger()

def get_scrawler_type_key(
    source_type: SourceProviderType, source_sub_type: SourceSubProviderType
) -> str:
    return f"{source_type}_{source_sub_type}"


# 定义爬虫映射
crawler_map = {
    get_scrawler_type_key(
        SourceProviderType.website, SourceSubProviderType.hacker_news
    ): sync_hacker_news,
    get_scrawler_type_key(
        SourceProviderType.rss, SourceSubProviderType.default
    ): sync_rss,
    get_scrawler_type_key(
        SourceProviderType.rss, SourceSubProviderType.wx_mp
    ): sync_rss,
    get_scrawler_type_key(
        SourceProviderType.email, SourceSubProviderType.default
    ): sync_mailbox,
}

# 初始化爬虫管理器
crawler_manager = CrawlerManager(get_redis_client(), crawler_map)




@celery_app.task(
    bind=True,
)
def crawl_datasource(self, source_id: int):
    """
    启动爬虫任务，使用分布式锁确保同一数据源不会同时执行多个任务

    Args:
        source_data: 序列化后的SourceProvider数据
    """
    try:
        with get_db_context() as session:
            source = session.get(SourceProvider, source_id)
            assert source is not None
            schema = SourceProviderSchema.model_validate(source)
        crawler_manager.execute_task(schema)
    except ValueError as e:
        logger.error(f"数据源反序列化失败: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"爬虫任务执行失败: {str(e)}")
        raise


@celery_app.task(bind=True, name="keep_sync_sources")
def keep_sync_sources(self):
    """
    激活爬虫任务，扫描所有数据源
    """
    logger.info("启动数据源同步")
    with get_db_context() as db:
        try:
            stmt = select(SourceProvider)
            for source in db.scalars(stmt):
                try:
                    crawl_datasource.delay(source.id)
                except Exception as e:
                    logger.error(
                        f"创建任务失败 - 数据源: {source.nickname}, 错误: {str(e)}"
                    )
        except Exception as e:
            logger.error(f"同步服务异常: {str(e)}", exc_info=True)
        finally:
            if "db" in locals():
                db.close()
