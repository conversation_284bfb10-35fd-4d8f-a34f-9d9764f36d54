from app.logging import get_logger

from tasks.celery_app import celery_app
from app.db.models import IncompleteArticle, CompleteArticle
from app.service.web_crawler import general_crawl
from app.db.session import get_db_context
from sqlalchemy.orm import Session
from tasks.classify.summarize_tasks import summarize_complete_article

logger = get_logger()


@celery_app.task(bind=True)
def fetch_incomplete_article(self, incomplete_article_id: int):
    try:
        with get_db_context() as session:
            incomplete_article = session.get(IncompleteArticle, incomplete_article_id)
            assert incomplete_article is not None
            session.query(IncompleteArticle).filter(
                IncompleteArticle.id == incomplete_article_id
            ).update({IncompleteArticle.expanded: True})
            id = _fetch_incomplete_article(incomplete_article, session)
        summarize_complete_article.delay(id)
    except Exception as e:
        logger.error(f"邮件内容抽取失败: {str(e)}")
        raise
    pass


def _fetch_incomplete_article(incomplete_article: IncompleteArticle, session: Session):
    if incomplete_article.link is None:
        logger.warning(f"Failed to fetch article with no link: {incomplete_article.id}")
        return
    content = general_crawl(incomplete_article.link)
    if content is None:
        logger.warning(f"Failed to fetch article content: {incomplete_article.id}")
        return

    content = content.replace("\x00", "")
    complete_article = CompleteArticle(
        origin_url=incomplete_article.link,
        source_id=incomplete_article.source_id,
        origin_id=incomplete_article.id,
        title=incomplete_article.headline,
        content=content,
        ts=incomplete_article.ts,
    )
    session.add(complete_article)
    session.flush()
    return complete_article.id
