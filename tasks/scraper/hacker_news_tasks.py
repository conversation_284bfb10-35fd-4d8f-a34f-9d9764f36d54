from typing import List
from sqlalchemy import select
from fastapi import HTTPException
import requests

from app.db.models import (
    CompleteArticle,
    HackerNewsSource,
    SourceProvider,
    SourceProviderType,
)
from app.db.session import Session, get_db_context
from app.service.web_crawler import general_crawl
from tasks.celery_app import celery_app
from tasks.classify.summarize_tasks import summarize_complete_article
from app.logging import get_logger

logger = get_logger()

# Base URL for the Hacker News API
BASE_URL = "https://hacker-news.firebaseio.com/v0"

@celery_app.task(bind=True)
def extract_hn_story(self, hn_id: int):
    try:
        with get_db_context() as session:
            hn = session.get(HackerNewsSource, hn_id)
            assert hn is not None
            session.query(HackerNewsSource).filter(HackerNewsSource.id == hn_id).update(
                {HackerNewsSource.extracted: True}
            )
            _extract_hn_story(hn)
    except ValueError as e:
        logger.error(f"hn反序列化失败, id {hn_id}, error {str(e)}")
        raise
    except Exception as e:
        logger.error(f"hn内容抽取失败, id {hn_id}, error {str(e)}")
        raise
    pass


def _extract_hn_story(story: HackerNewsSource):
    with get_db_context() as session:
        if story.out_link is None:
            content_md = story.content
        else:
            content_md = general_crawl(story.out_link)
            if content_md is None:
                logger.warning(
                    f"Failed to convert webpage to markdown for story {story.story_id}"
                )
                return

        if content_md is None:
            content_md = ""
        content_md = content_md.replace("\x00", "")
        new_article = CompleteArticle(
            source_id=story.source_id,
            origin_url=f"https://news.ycombinator.com/item?id={story.story_id}",
            origin_id=story.id,
            content=content_md,
            title=story.title,
            summarized=False,
            ts=story.ts,
        )
        story.extracted = True
        session.add(new_article)
        session.flush()
        summarize_complete_article.delay(new_article.id)


def fetch_json(url):
    """
    使用 requests 从给定 URL 获取 JSON 数据。

    Args:
        url (str): 要获取数据的 URL。

    Returns:
        dict: 作为字典的 JSON 响应。
    """
    response = requests.get(url)
    response.raise_for_status()
    return response.json()


def fetch_top_stories(db: Session):
    """
    从 Hacker News 获取热门故事。

    Args:
        db: 数据库会话

    Returns:
        list: 包含故事详情的字典列表。
    """
    # 获取热门故事 ID
    top_stories_url = f"{BASE_URL}/topstories.json"
    top_stories_ids = fetch_json(top_stories_url)

    logger.info(f"Fetching details for {len(top_stories_ids)} top stories")
    stories = []
    # 获取热门故事的详细信息
    fetched = 0
    for i, story_id in enumerate(top_stories_ids, start=1):
        stmt = select(HackerNewsSource).where(HackerNewsSource.story_id == story_id)
        story = db.execute(stmt).scalar_one_or_none()
        if story is not None:
            continue
        logger.info(f"Fetching details for story {i}/{len(top_stories_ids)}")
        story_data = fetch_json(f"{BASE_URL}/item/{story_id}.json")
        if story_data is None:
            logger.warning(f"Failed to fetch data for story {story_id}")
            continue
        fetched += 1

        # 从故事数据中提取相关详细信息
        story = {
            "story_id": story_data.get("id"),
            "title": story_data.get("title"),
            "content": story_data.get("text"),
            "out_link": story_data.get("url"),
            "score": story_data.get("score"),
            "author": story_data.get("by"),
            "comments": story_data.get("kids"),
            "ts": story_data.get("time"),
        }
        stories.append(story)

    logger.info(f"Fetched details for {fetched}/{len(top_stories_ids)} top stories")
    return stories


def sync_hacker_news(source: SourceProvider):
    """
    同步Hacker News提供者的数据

    Args:
        provider: 源提供者
        session: 数据库会话
    """
    if source.source_type != SourceProviderType.website:
        raise HTTPException(status_code=400, detail="Provider type must be hacker_news")
    logger.info(f"Syncing hacker news provider {source}")
    with get_db_context() as db:
        ids = sync_top_stories(source.id, db)
    if ids is None:
        return
    for id in ids:
        extract_hn_story.delay(id)


def sync_top_stories(source_id: int | None, db: Session)->List[int] | None:
    """
    同步Hacker News热门故事

    Args:
        source_id: 提供者ID
        db: 数据库会话
    """
    if source_id is None:
        logger.error("Provider ID is required to sync top stories")
        return None
    # 获取并显示热门故事
    top_stories = fetch_top_stories(db)
    ids = []
    for story in top_stories:
        top_story = HackerNewsSource(**story)
        top_story.source_id = source_id
        db.add(top_story)
        db.flush()
        ids.append(top_story.id)

    logger.info(f"Synced top stories for provider {source_id}")
    return ids
