#!/usr/bin/env python3
"""
Command-line script to add Chinese summaries to existing TopicContent records.
This can be run directly to perform the translation process.
"""

import argparse
import os
import sys
import time

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.logging import get_logger

# Import the function from our module
from tasks.add_chinese_summaries import add_chinese_summaries
# Import the Celery task if needed
from tasks.translate_summaries_task import translate_all_summaries

logger = get_logger()

def main():
    """
    Main entry point for the command-line script.
    """
    parser = argparse.ArgumentParser(description='Add Chinese summaries to existing TopicContent records')
    parser.add_argument('--celery', action='store_true', help='Run as a Celery task instead of directly')
    args = parser.parse_args()

    if args.celery:
        # Run as a Celery task
        logger.info("Starting Chinese summary translation as a Celery task...")
        task = translate_all_summaries.delay()
        logger.info(f"Task started with ID: {task.id}")
        logger.info("The task is running in the background. Check Celery logs for progress.")
    else:
        # Run directly
        logger.info("Starting Chinese summary translation process directly...")
        start_time = time.time()
        add_chinese_summaries()
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"Chinese summary translation process completed in {duration:.2f} seconds.")

if __name__ == "__main__":
    main()
