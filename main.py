import signal
import sys
import threading
import traceback
from app.logging import setup_logging,get_logger

logger = get_logger()

def signal_handler(signum, frame):
    logger.warning("Received interrupt signal, dumping threads stack traces...")

    # Get all threads
    threads = {t.ident: t for t in threading.enumerate()}

    for thread_id, stack in sys._current_frames().items():
        thread = threads.get(thread_id)
        logger.info(f"\nThread {thread_id} ({thread.name if thread else 'Unknown'}):")
        for filename, lineno, name, line in traceback.extract_stack(stack):
            logger.info(f'  File "{filename}", line {lineno}, in {name}')
            if line:
                logger.info(f"    {line.strip()}")

    logger.warning("Shutting down application...")
    sys.exit(1)


if __name__ == "__main__":
    import uvicorn

    # Register signal handler
    signal.signal(signal.SIGINT, signal_handler)

    # Start uvicorn server
    uvicorn.run("app.app:app", host="0.0.0.0", port=8001, reload=False, workers=1)
