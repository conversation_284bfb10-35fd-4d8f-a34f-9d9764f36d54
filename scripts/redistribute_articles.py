#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script to redistribute articles from a specific source with article_id greater than a specified value.
This script is used after related database records have been deleted to reprocess articles.
"""

import argparse
import sys
import time
from sqlalchemy import select, and_

# Add the project root to the Python path
sys.path.append(".")

from app.db.session import get_db_context
from app.db.models import SummarizedArticle
from tasks.topic.topic_router_tasks import distribute_article
from app.logging import get_logger

logger = get_logger()

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Redistribute articles for processing.')
    parser.add_argument('--source-id', type=int, required=True,
                        help='The source_id to filter articles by')
    parser.add_argument('--min-article-id', type=int, required=True,
                        help='The minimum article_id to process (exclusive)')
    parser.add_argument('--dry-run', action='store_true',
                        help='Only print what would be done without actually submitting tasks')
    parser.add_argument('--limit', type=int, default=None,
                        help='Limit the number of articles to process')
    parser.add_argument('--batch-size', type=int, default=10,
                        help='Number of articles to process in each batch (default: 10)')
    parser.add_argument('--batch-delay', type=float, default=1.0,
                        help='Delay in seconds between batches (default: 1.0)')
    return parser.parse_args()

def main():
    """Main function to redistribute articles."""
    args = parse_arguments()

    source_id = args.source_id
    min_article_id = args.min_article_id
    dry_run = args.dry_run
    limit = args.limit
    batch_size = args.batch_size
    batch_delay = args.batch_delay

    logger.info(f"Starting redistribution of articles for source_id={source_id} with article_id > {min_article_id}")
    logger.info(f"Using batch size: {batch_size}, batch delay: {batch_delay} seconds")

    try:
        with get_db_context() as db:
            # Query for articles matching the criteria
            stmt = select(SummarizedArticle).where(
                and_(
                    SummarizedArticle.source_id == source_id,
                    SummarizedArticle.id > min_article_id
                )
            ).order_by(SummarizedArticle.id)

            if limit:
                stmt = stmt.limit(limit)

            articles = db.execute(stmt).scalars().all()

            if not articles:
                logger.info(f"No articles found for source_id={source_id} with article_id > {min_article_id}")
                return

            total_articles = len(articles)
            logger.info(f"Found {total_articles} articles to redistribute")

            # Process articles in batches
            for i in range(0, total_articles, batch_size):
                batch = articles[i:i+batch_size]
                batch_num = i // batch_size + 1
                total_batches = (total_articles + batch_size - 1) // batch_size

                logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch)} articles)")

                for article in batch:
                    if dry_run:
                        logger.info(f"[DRY RUN] Would redistribute article_id={article.id} from source_id={article.source_id}")
                    else:
                        logger.info(f"Redistributing article_id={article.id} from source_id={article.source_id}")
                        # Submit the task to Celery
                        distribute_article.delay(article.id, article.source_id)

                # Add delay between batches (except after the last batch)
                if i + batch_size < total_articles and batch_delay > 0 and not dry_run:
                    logger.info(f"Waiting {batch_delay} seconds before next batch...")
                    time.sleep(batch_delay)

            logger.info(f"Completed redistribution of {total_articles} articles")

    except Exception as e:
        logger.error(f"Error during redistribution: {str(e)}")
        raise

if __name__ == "__main__":
    main()
