# 数据源 Actor
- 按照数据新鲜程度刷新数据源
- 发送到数据源智能层
## 数据源元数据
- 将所有支持的信息源转换为有游标且支持分页的 rss 格式。
- 去重、对于同一个实际数据源在这个表里只有一个记录。
- 记录反向依赖关系
## 数据源内容
- 保存每个数据源抓取到的原始内容

# 数据智能层
- 识别列表形式的原始数据源，展开后保存到数据源内容表里。
- 总结每个完整的文章，并生成向量。
- 将处理后的【已总结文章】分发到用户 topic Actor 上。
## 用户派生数据源元数据
- 分用户的数据源、支持嵌套文件夹、(暂时不支持简单或者复杂的过滤器)。
- 每个数据源有一个底层数据源，可能多个用户数据源对应同一个底层数据源。（用户不同、（暂时不支持过滤器不同都）会导致派生数据源数量增加）

# Classify Actor
- 实际情况中，每个数据源生成的内容本身具有一定主题。用户不会让一个 source 作为多个 topic 的素材。理论上 source 是 topic 的若干倍数。所以不应该把所有 source 的文章都一起参与到 topic 的计算中。只应该把 topic 相关的 source 纳入到 topic 的计算中。这样放大的倍数更小。
- 根据向量和 LLM 为文章分配 topic，并且记录 topic 与文章的关系。
- 对于每个新 topic，依次计算所有 filter 的匹配程度，最终确定是否应该呈现给用户。