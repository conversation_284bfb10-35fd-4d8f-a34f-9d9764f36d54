#!/usr/bin/env python3
"""
直接测试Celery管理功能的脚本
"""

import sys
import os
sys.path.append('.')

from app.db.session import get_db_context
from app.admin_web.celery_admin import get_failed_tasks
from sqlalchemy import text

def test_get_failed_tasks():
    """测试获取失败任务的功能"""
    print("Testing get_failed_tasks function...")
    
    try:
        with get_db_context() as db:
            # 测试获取失败任务
            failed_tasks, total_count = get_failed_tasks(db, page=1, page_size=5)
            
            print(f"Total failed tasks: {total_count}")
            print(f"Retrieved {len(failed_tasks)} tasks")
            
            if failed_tasks:
                print("\nFirst few failed tasks:")
                for i, task in enumerate(failed_tasks[:3]):
                    print(f"  {i+1}. Task ID: {task['task_id'][:16]}...")
                    print(f"     Name: {task['name'] or 'Unknown'}")
                    print(f"     Error: {task['error_message'][:100]}...")
                    print(f"     Date: {task['date_done']}")
                    print(f"     Retries: {task['retries']}")
                    print()
            else:
                print("No failed tasks found")
                
    except Exception as e:
        print(f"Error testing get_failed_tasks: {e}")
        import traceback
        traceback.print_exc()

def test_database_connection():
    """测试数据库连接"""
    print("Testing database connection...")
    
    try:
        with get_db_context() as db:
            # 测试基本查询
            result = db.execute(text("SELECT COUNT(*) FROM feeds.celery_taskmeta"))
            total_tasks = result.scalar()
            print(f"Total tasks in database: {total_tasks}")
            
            # 测试失败任务查询
            result = db.execute(text("SELECT COUNT(*) FROM feeds.celery_taskmeta WHERE status = 'FAILURE'"))
            failed_count = result.scalar()
            print(f"Failed tasks: {failed_count}")
            
            # 测试成功任务查询
            result = db.execute(text("SELECT COUNT(*) FROM feeds.celery_taskmeta WHERE status = 'SUCCESS'"))
            success_count = result.scalar()
            print(f"Successful tasks: {success_count}")
            
            # 测试其他状态
            result = db.execute(text("SELECT status, COUNT(*) FROM feeds.celery_taskmeta GROUP BY status"))
            status_counts = result.fetchall()
            print("\nTask status breakdown:")
            for status, count in status_counts:
                print(f"  {status}: {count}")
                
    except Exception as e:
        print(f"Error testing database connection: {e}")
        import traceback
        traceback.print_exc()

def test_task_details():
    """测试获取任务详情"""
    print("\nTesting task details...")
    
    try:
        with get_db_context() as db:
            # 获取一个失败任务的详情
            result = db.execute(text("""
                SELECT task_id, name, args, kwargs, result, traceback 
                FROM feeds.celery_taskmeta 
                WHERE status = 'FAILURE' 
                LIMIT 1
            """))
            task = result.fetchone()
            
            if task:
                print(f"Sample failed task:")
                print(f"  Task ID: {task.task_id}")
                print(f"  Name: {task.name or 'Unknown'}")
                print(f"  Has args: {task.args is not None}")
                print(f"  Has kwargs: {task.kwargs is not None}")
                print(f"  Has result: {task.result is not None}")
                print(f"  Has traceback: {task.traceback is not None}")
                
                # 尝试解析参数
                if task.args:
                    try:
                        import pickle
                        args = pickle.loads(task.args)
                        print(f"  Args: {args}")
                    except Exception as e:
                        print(f"  Args parsing error: {e}")
                
                if task.kwargs:
                    try:
                        import pickle
                        kwargs = pickle.loads(task.kwargs)
                        print(f"  Kwargs: {kwargs}")
                    except Exception as e:
                        print(f"  Kwargs parsing error: {e}")
                        
                if task.result:
                    try:
                        import pickle
                        result_data = pickle.loads(task.result)
                        print(f"  Result type: {type(result_data)}")
                        if isinstance(result_data, dict):
                            print(f"  Error message: {result_data.get('exc_message', 'N/A')}")
                            print(f"  Error type: {result_data.get('exc_type', 'N/A')}")
                    except Exception as e:
                        print(f"  Result parsing error: {e}")
            else:
                print("No failed tasks found for detail testing")
                
    except Exception as e:
        print(f"Error testing task details: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("Celery Admin Functions Test")
    print("=" * 50)
    
    # 测试数据库连接
    test_database_connection()
    
    print("\n" + "=" * 50)
    
    # 测试获取失败任务
    test_get_failed_tasks()
    
    print("\n" + "=" * 50)
    
    # 测试任务详情
    test_task_details()
    
    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    main()
