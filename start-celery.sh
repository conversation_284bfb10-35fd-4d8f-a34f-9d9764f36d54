#!/bin/bash

# Set Celery-specific environment variables
export CELERY_LOG_LEVEL=${CELERY_LOG_LEVEL:-INFO}
export LOG_PATH=${LOG_PATH:-logs}

# Start Celery beat scheduler in the background
celery -A tasks.celery_app beat -l $CELERY_LOG_LEVEL &

# Start a single Celery worker without beat scheduler
# Reduced concurrency from 1000 to 100 to prevent database connection pool exhaustion
celery -A tasks.celery_app worker -P gevent -c 10 -l $CELERY_LOG_LEVEL
