#!/usr/bin/env python3
"""
Celery管理界面演示脚本
"""

import sys
import os
sys.path.append('.')

from app.db.session import get_db_context
from app.admin_web.celery_admin import get_failed_tasks
from sqlalchemy import text

def demo_overview():
    """演示系统概览"""
    print("🚀 Celery任务管理界面演示")
    print("=" * 60)
    
    with get_db_context() as db:
        # 获取总体统计
        total_tasks = db.execute(text("SELECT COUNT(*) FROM feeds.celery_taskmeta")).scalar()
        failed_tasks = db.execute(text("SELECT COUNT(*) FROM feeds.celery_taskmeta WHERE status = 'FAILURE'")).scalar()
        success_tasks = db.execute(text("SELECT COUNT(*) FROM feeds.celery_taskmeta WHERE status = 'SUCCESS'")).scalar()
        
        print(f"📊 任务统计:")
        print(f"   总任务数: {total_tasks}")
        print(f"   成功任务: {success_tasks}")
        print(f"   失败任务: {failed_tasks}")
        print(f"   成功率: {(success_tasks/total_tasks*100):.1f}%")
        print()

def demo_failed_tasks():
    """演示失败任务查看功能"""
    print("❌ 失败任务详情:")
    print("-" * 40)
    
    with get_db_context() as db:
        failed_tasks, total_count = get_failed_tasks(db, page=1, page_size=3)
        
        for i, task in enumerate(failed_tasks, 1):
            print(f"{i}. 任务ID: {task['task_id'][:16]}...")
            print(f"   名称: {task['name'] or '未知任务'}")
            print(f"   错误类型: {task['error_type'] or '未知'}")
            print(f"   错误信息: {task['error_message'][:80]}...")
            print(f"   失败时间: {task['date_done']}")
            print(f"   重试次数: {task['retries']}")
            print()

def demo_error_analysis():
    """演示错误分析功能"""
    print("🔍 错误分析:")
    print("-" * 40)
    
    with get_db_context() as db:
        # 分析常见错误类型
        error_query = text("""
            SELECT 
                CASE 
                    WHEN result LIKE '%DNS%' OR result LIKE '%name resolution%' THEN 'DNS解析错误'
                    WHEN result LIKE '%AuthenticationError%' THEN '认证错误'
                    WHEN result LIKE '%ConnectionError%' THEN '连接错误'
                    WHEN result LIKE '%TimeoutError%' THEN '超时错误'
                    ELSE '其他错误'
                END as error_category,
                COUNT(*) as count
            FROM feeds.celery_taskmeta 
            WHERE status = 'FAILURE' 
            GROUP BY error_category
            ORDER BY count DESC
        """)
        
        results = db.execute(error_query).fetchall()
        
        for error_type, count in results:
            print(f"   {error_type}: {count} 个任务")
        print()

def demo_features():
    """演示功能特性"""
    print("✨ 主要功能特性:")
    print("-" * 40)
    
    features = [
        "📋 失败任务列表查看 - 分页显示，支持搜索",
        "🔄 批量重试功能 - 选择多个任务一键重试",
        "🗑️  清理旧任务 - 定期清理过期的失败任务",
        "📊 统计仪表板 - 实时显示任务状态统计",
        "🔍 详细错误信息 - 查看完整的错误堆栈和参数",
        "🛡️  权限控制 - 只有管理员可以访问",
        "💻 响应式界面 - 适配各种设备屏幕",
        "⚡ 实时更新 - 支持手动刷新获取最新状态"
    ]
    
    for feature in features:
        print(f"   {feature}")
    print()

def demo_usage():
    """演示使用方法"""
    print("📖 使用方法:")
    print("-" * 40)
    
    steps = [
        "1. 访问 http://localhost:8001/admin/login",
        "2. 使用管理员账户登录",
        "3. 点击导航栏中的 'Celery Tasks'",
        "4. 查看失败任务列表",
        "5. 选择要重试的任务，点击 'Retry' 按钮",
        "6. 使用 'Cleanup Old Tasks' 清理旧任务",
        "7. 点击任务ID查看详细信息"
    ]
    
    for step in steps:
        print(f"   {step}")
    print()

def demo_api_endpoints():
    """演示API端点"""
    print("🌐 API端点:")
    print("-" * 40)
    
    endpoints = [
        "GET  /admin/celery-tasks - 任务列表页面",
        "POST /admin/celery-tasks/retry - 重试选中的任务",
        "GET  /admin/celery-tasks/{task_id}/detail - 任务详情页面",
        "POST /admin/celery-tasks/cleanup - 清理旧任务",
        "GET  /admin - 管理员仪表板（包含任务统计）"
    ]
    
    for endpoint in endpoints:
        print(f"   {endpoint}")
    print()

def main():
    """主演示函数"""
    try:
        demo_overview()
        demo_failed_tasks()
        demo_error_analysis()
        demo_features()
        demo_usage()
        demo_api_endpoints()
        
        print("🎉 演示完成！")
        print("💡 提示: 访问 http://localhost:8001/admin 开始使用管理界面")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
