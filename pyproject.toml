[project]
name = "backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
  "beautifulsoup4==4.12.3",
  "celery>=5.4.0",
  "fastapi[standard]==0.115.6",
  "httpx==0.28.0",
  "inscriptis==2.5.0",
  "loguru==0.7.2",
  "mail-parser>=4.1.2",
  "psycopg2-binary>=2.9.10",
  "psycopg==3.2.3",
  "pydantic==2.10.3",
  "pydantic-core==2.27.1",
  "readability-lxml==0.8.1",
  "requests==2.32.3",
  "schema==0.7.7",
  "streamlit==1.36.0",
  "trafilatura==2.0.0",
  "uvicorn==0.32.1",
  "redis>=5.2.1",
  "aioredis>=2.0.1",
  "alembic>=1.14.0",
  "json-repair>=0.30.3",
  "reader>=3.16",
  "aiohttp>=3.11.10",
  "ruff>=0.8.3",
  "crawl4ai>=0.4.23",
  "pgvector>=0.3.6",
  "litellm>=1.53.3",
  "feedparser>=6.0.11",
  "alembic-postgresql-enum>=1.5.0",
  "aioimaplib>=2.0.1",
  "tldextract>=5.1.3",
  "gevent>=24.11.1",
  "python-multipart>=0.0.20",
  "jose>=1.0.0",
  "pyjwt>=2.10.1",
  "passlib[bcrypt]>=1.7.4",
  "bcrypt>=4.3.0",
  "flower>=2.0.1",
  "python-dotenv>=1.1.0",
]

[project.optional-dependencies]
test = [
  "pytest>=8.0.0",
  "pytest-asyncio>=0.23.5",
  "pytest-cov>=4.1.0",
  "pytest-mock>=3.12.0",
]

[tool.pytest.ini_options]
pythonpath = ["."]
asyncio_mode = "auto"

[tool.uv]
upgrade = true

[tool.uv.pip]
upgrade = true
