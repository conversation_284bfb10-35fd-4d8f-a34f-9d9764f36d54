import pytest
import aiohttp
from unittest.mock import MagicMock, patch
from app.service.scraper.website.general_website import get_website_metadata
from app.service.scraper.rss.rss import get_rss_metadata

TEST_RSS_URL = "http://192.168.3.170:4000/feeds/MP_WXS_3236757533.atom"
RSS_RESPONSE = """<?xml version="1.0" encoding="utf-8"?>
<feed xmlns="http://www.w3.org/2005/Atom">
  <title>测试 RSS 源</title>
  <link href="http://example.com"/>
  <icon>http://example.com/icon.png</icon>
</feed>
"""

WEBSITE_HTML = """
<!DOCTYPE html>
<html>
<head>
    <title>测试网站</title>
    <link rel="icon" href="/favicon.ico">
    <meta property="og:site_name" content="测试站点名称">
</head>
<body>
    <h1>Welcome</h1>
</body>
</html>
"""


@pytest.fixture
def mock_response():
    response = MagicMock()
    response.status = 200
    return response


@pytest.mark.asyncio
async def test_get_rss_metadata_success(mock_response):
    mock_response.text.return_value = RSS_RESPONSE

    with patch("aiohttp.ClientSession.get") as mock_get:
        mock_get.return_value.__aenter__.return_value = mock_response
        name, icon = await get_rss_metadata(TEST_RSS_URL)

        assert name == "测试 RSS 源"
        assert icon == "http://example.com/icon.png"


@pytest.mark.asyncio
async def test_get_rss_metadata_no_icon(mock_response):
    # 测试没有图标时的情况，应该生成一个 ui-avatars 图标
    rss_no_icon = """<?xml version="1.0" encoding="utf-8"?>
    <feed xmlns="http://www.w3.org/2005/Atom">
      <title>测试 RSS 源</title>
      <link href="http://example.com"/>
    </feed>
    """
    mock_response.text.return_value = rss_no_icon

    with patch("aiohttp.ClientSession.get") as mock_get:
        mock_get.return_value.__aenter__.return_value = mock_response
        name, icon = await get_rss_metadata(TEST_RSS_URL)

        assert name == "测试 RSS 源"
        assert "ui-avatars.com" in icon
        assert "name=测试 RSS 源" in icon


@pytest.mark.asyncio
async def test_get_rss_metadata_http_error(mock_response):
    mock_response.status = 404

    with patch("aiohttp.ClientSession.get") as mock_get:
        mock_get.return_value.__aenter__.return_value = mock_response
        name, icon = await get_rss_metadata(TEST_RSS_URL)

        assert name is None
        assert icon is None


@pytest.mark.asyncio
async def test_get_website_metadata_success(mock_response):
    mock_response.text.return_value = WEBSITE_HTML

    with patch("aiohttp.ClientSession.get") as mock_get:
        mock_get.return_value.__aenter__.return_value = mock_response
        name, icon = await get_website_metadata("https://example.com")

        assert name == "测试站点名称"
        assert icon == "https://example.com/favicon.ico"


@pytest.mark.asyncio
async def test_website_metadata_no_metadata(mock_response):
    # 测试网站没有元数据的情况
    html_no_metadata = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Simple Site</title>
    </head>
    <body>
        <h1>Welcome</h1>
    </body>
    </html>
    """
    mock_response.text.return_value = html_no_metadata

    with patch("aiohttp.ClientSession.get") as mock_get:
        mock_get.return_value.__aenter__.return_value = mock_response
        name, icon = await get_website_metadata("https://example.com")

        assert name == "Simple Site"  # 应该使用 title 标签内容
        assert "ui-avatars.com" in icon  # 应该生成一个 avatar


@pytest.mark.asyncio
async def test_get_website_metadata_http_error(mock_response):
    mock_response.status = 500

    with patch("aiohttp.ClientSession.get") as mock_get:
        mock_get.return_value.__aenter__.return_value = mock_response
        name, icon = await get_website_metadata("https://example.com")

        assert name is None
        assert icon is None


@pytest.mark.asyncio
async def test_get_website_metadata_connection_error():
    with patch("aiohttp.ClientSession.get") as mock_get:
        mock_get.side_effect = aiohttp.ClientError("Connection error")
        name, icon = await get_website_metadata("https://invalid-url.com")

        assert name is None
        assert icon is None
