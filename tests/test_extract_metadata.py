import pytest
from unittest.mock import patch, MagicMock, AsyncMock
import aiohttp
import json
from app.util.llms import extract_metadata, _extract_website_metadata, _extract_rss_source_website

# 测试数据
WEBSITE_HTML = """
<!DOCTYPE html>
<html>
<head>
    <title>测试网站</title>
    <link rel="icon" href="/favicon.ico">
    <meta property="og:site_name" content="测试站点名称">
</head>
<body>
    <h1>Welcome</h1>
</body>
</html>
"""

RSS_CONTENT = """<?xml version="1.0" encoding="utf-8"?>
<feed xmlns="http://www.w3.org/2005/Atom">
  <title>测试 RSS 源</title>
  <link href="http://example.com"/>
  <icon>http://example.com/icon.png</icon>
</feed>
"""

# 模拟 OpenAI API 响应
def create_mock_openai_response(content):
    response = MagicMock()
    response.choices = [MagicMock(message=MagicMock(content=content))]
    return response

@pytest.fixture
def mock_aiohttp_response():
    """创建模拟的 aiohttp 响应"""
    async def mock_response():
        response = AsyncMock()
        response.status = 200
        response.headers = {
            "content-type": "text/html",
            "server": "nginx"
        }
        response.text = AsyncMock(return_value=WEBSITE_HTML)
        return response
    return mock_response

@pytest.fixture
def mock_openai():
    """创建模拟的 OpenAI 客户端"""
    with patch("app.util.llms.openai_client") as mock:
        mock.chat.completions.create = MagicMock()
        yield mock

@pytest.mark.asyncio
async def test_extract_website_metadata(mock_openai):
    """测试从网站内容提取元数据"""
    metadata = {
        "name": "测试网站",
        "icon_url": "http://example.com/favicon.ico"
    }
    mock_openai.chat.completions.create.return_value = create_mock_openai_response(
        json.dumps(metadata)
    )

    name, icon = await _extract_website_metadata(
        {"content-type": "text/html"},
        WEBSITE_HTML
    )
    
    assert name == "测试网站"
    assert icon == "http://example.com/favicon.ico"
    mock_openai.chat.completions.create.assert_called_once()

@pytest.mark.asyncio
async def test_extract_rss_source_website(mock_openai):
    """测试从 RSS 内容提取源网站 URL"""
    mock_openai.chat.completions.create.return_value = create_mock_openai_response(
        "http://example.com"
    )

    source_url = await _extract_rss_source_website(RSS_CONTENT)
    assert source_url == "http://example.com"
    mock_openai.chat.completions.create.assert_called_once()

@pytest.mark.asyncio
async def test_extract_metadata_website(mock_openai, mock_aiohttp_response):
    """测试从网站 URL 提取元数据"""
    metadata = {
        "name": "测试网站",
        "icon_url": "http://example.com/favicon.ico"
    }
    mock_openai.chat.completions.create.return_value = create_mock_openai_response(
        json.dumps(metadata)
    )

    with patch("aiohttp.ClientSession") as mock_session:
        mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__ = mock_aiohttp_response
        
        name, icon = await extract_metadata("http://example.com")
        assert name == "测试网站"
        assert icon == "http://example.com/favicon.ico"

@pytest.mark.asyncio
async def test_extract_metadata_rss(mock_openai, mock_aiohttp_response):
    """测试从 RSS URL 提取元数据，包括回退到源网站的情况"""
    # 配置模拟响应序列
    mock_openai.chat.completions.create.side_effect = [
        # 第一次调用：从 RSS 直接提取失败
        create_mock_openai_response(json.dumps({"name": None, "icon_url": None})),
        # 第二次调用：获取源网站 URL
        create_mock_openai_response("http://example.com"),
        # 第三次调用：从源网站获取元数据成功
        create_mock_openai_response(json.dumps({
            "name": "测试网站",
            "icon_url": "http://example.com/favicon.ico"
        }))
    ]

    # 配置 RSS 响应
    async def mock_rss_response():
        response = AsyncMock()
        response.status = 200
        response.headers = {"content-type": "application/xml"}
        response.text = AsyncMock(return_value=RSS_CONTENT)
        return response

    with patch("aiohttp.ClientSession") as mock_session:
        mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.side_effect = [
            mock_rss_response(),  # RSS 请求
            mock_aiohttp_response()  # 源网站请求
        ]
        
        name, icon = await extract_metadata("http://example.com/rss.xml")
        assert name == "测试网站"
        assert icon == "http://example.com/favicon.ico"
        assert mock_openai.chat.completions.create.call_count == 3

@pytest.mark.asyncio
async def test_extract_metadata_connection_error():
    """测试网络连接错误的处理"""
    with patch("aiohttp.ClientSession") as mock_session:
        mock_session.return_value.__aenter__.return_value.get.side_effect = \
            aiohttp.ClientError("Connection failed")
        
        name, icon = await extract_metadata("http://example.com")
        assert name is None
        assert icon is None

@pytest.mark.asyncio
async def test_extract_metadata_invalid_response(mock_openai, mock_aiohttp_response):
    """测试 OpenAI API 返回无效响应的处理"""
    # 配置返回无效的 JSON
    mock_openai.chat.completions.create.return_value = create_mock_openai_response(
        "Invalid JSON"
    )

    with patch("aiohttp.ClientSession") as mock_session:
        mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__ = mock_aiohttp_response
        
        name, icon = await extract_metadata("http://example.com")
        assert name is None
        assert icon is None

@pytest.mark.asyncio
async def test_extract_metadata_http_error():
    """测试 HTTP 错误响应的处理"""
    async def mock_error_response():
        response = AsyncMock()
        response.status = 404
        return response

    with patch("aiohttp.ClientSession") as mock_session:
        mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__ = \
            mock_error_response
        
        name, icon = await extract_metadata("http://example.com")
        assert name is None
        assert icon is None