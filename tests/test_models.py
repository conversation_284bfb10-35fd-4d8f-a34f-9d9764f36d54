from enum import Enum
from typing import Optional
from pydantic import BaseModel, model_validator
import pytest
from pydantic import ValidationError

class SourceProviderType(str, Enum):
    RSS = "rss"
    WEBSITE = "website"
    EMAIL = "email"

class SubSourceProviderType(str, Enum):
    DEFAULT = "default"
    WX_MP = "wx_mp"
    GITHUB = "github"
    HACKER_NEWS = "hacker_news"
    GOOGLE_NEWS = "google_news"

class CreateProviderRequest(BaseModel):
    provider_type: SourceProviderType
    sub_provider_type: SubSourceProviderType
    url: Optional[str] = None
    name: str
    start_time: Optional[int] = None

    # for email
    imap_server: Optional[str] = None
    imap_port: Optional[int] = None
    user: Optional[str] = None
    password: Optional[str] = None

    # for searchable news website
    keywords: Optional[str] = None

    @model_validator(mode='after')
    def validate_fields(self) -> 'CreateProviderRequest':
        if self.provider_type in [SourceProviderType.rss, SourceProviderType.website]:
            if not self.url:
                raise ValueError(f"URL is required for {self.provider_type.value} provider")
                
        if self.provider_type == SourceProviderType.email:
            required_fields = {
                'imap_server': self.imap_server,
                'imap_port': self.imap_port,
                'user': self.user,
                'password': self.password
            }
            missing_fields = [k for k, v in required_fields.items() if not v]
            if missing_fields:
                raise ValueError(f"Fields {', '.join(missing_fields)} are required for email provider")
                
        return self


def test_rss_source_validation():
    # Test valid RSS provider
    valid_data = {
        "provider_type": SourceProviderType.rss,
        "sub_provider_type": SubSourceProviderType.default,
        "url": "http://example.com/rss",
        "name": "Test RSS",
        "start_time": None
    }
    provider = CreateProviderRequest(**valid_data)
    assert provider.provider_type == SourceProviderType.rss
    assert provider.url == "http://example.com/rss"

    # Test RSS provider without URL
    invalid_data = valid_data.copy()
    invalid_data["url"] = None
    with pytest.raises(ValidationError) as exc_info:
        CreateProviderRequest(**invalid_data)
    assert "URL is required for rss provider" in str(exc_info.value)


def test_website_source_validation():
    # Test valid website provider
    valid_data = {
        "provider_type": SourceProviderType.website,
        "sub_provider_type": SubSourceProviderType.default,
        "url": "http://example.com",
        "name": "Test Website",
        "start_time": None
    }
    provider = CreateProviderRequest(**valid_data)
    assert provider.provider_type == SourceProviderType.website
    assert provider.url == "http://example.com"

    # Test website provider without URL
    invalid_data = valid_data.copy()
    invalid_data["url"] = None
    with pytest.raises(ValidationError) as exc_info:
        CreateProviderRequest(**invalid_data)
    assert "URL is required for website provider" in str(exc_info.value)


def test_email_source_validation():
    # Test valid email provider
    valid_data = {
        "provider_type": SourceProviderType.email,
        "sub_provider_type": SubSourceProviderType.default,
        "name": "Test Email",
        "imap_server": "imap.example.com",
        "imap_port": 993,
        "user": "<EMAIL>",
        "password": "password123",
        "start_time": None
    }
    provider = CreateProviderRequest(**valid_data)
    assert provider.provider_type == SourceProviderType.email
    assert provider.imap_server == "imap.example.com"

    # Test email provider with missing fields
    invalid_data = valid_data.copy()
    invalid_data["imap_server"] = None
    invalid_data["password"] = None
    with pytest.raises(ValidationError) as exc_info:
        CreateProviderRequest(**invalid_data)
    error_str = str(exc_info.value)
    assert "Fields imap_server, password are required for email provider" in error_str


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
