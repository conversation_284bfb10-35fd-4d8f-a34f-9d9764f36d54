import pytest
from pydantic import ValidationError
from app.schemas.api import ParseSourceRequest
from app.db.models import SourceSubProviderType
from enum import Enum


class SourceProviderType(str, Enum):
    RSS = "rss"
    WEBSITE = "website"
    EMAIL = "email"


def test_rss_source_validation():
    # Test valid RSS provider
    valid_data = {
        "provider_type": SourceProviderType.RSS,
        "sub_provider_type": SourceSubProviderType.default,
        "url": "http://example.com/rss",
        "name": "Test RSS",
        "start_time": None,
    }
    provider = ParseSourceRequest(**valid_data)
    assert provider.source_type == SourceProviderType.RSS
    assert provider.url == "http://example.com/rss"

    # Test RSS provider without URL
    invalid_data = valid_data.copy()
    invalid_data["url"] = None
    with pytest.raises(ValidationError) as exc_info:
        ParseSourceRequest(**invalid_data)
    assert "URL is required for rss provider" in str(exc_info.value)


def test_website_source_validation():
    # Test valid website provider
    valid_data = {
        "provider_type": SourceProviderType.WEBSITE,
        "sub_provider_type": SourceSubProviderType.default,
        "url": "http://example.com",
        "name": "Test Website",
        "start_time": None,
    }
    provider = ParseSourceRequest(**valid_data)
    assert provider.source_type == SourceProviderType.WEBSITE
    assert provider.url == "http://example.com"

    # Test website provider without URL
    invalid_data = valid_data.copy()
    invalid_data["url"] = None
    with pytest.raises(ValidationError) as exc_info:
        ParseSourceRequest(**invalid_data)
    assert "URL is required for website provider" in str(exc_info.value)


def test_email_source_validation():
    # Test valid email provider
    valid_data = {
        "provider_type": SourceProviderType.EMAIL,
        "sub_provider_type": SourceSubProviderType.default,
        "name": "Test Email",
        "imap_server": "imap.example.com",
        "imap_port": 993,
        "user": "<EMAIL>",
        "password": "password123",
        "start_time": None,
    }
    provider = ParseSourceRequest(**valid_data)
    assert provider.source_type == SourceProviderType.EMAIL
    assert provider.imap_server == "imap.example.com"

    # Test email provider with missing fields
    invalid_data = valid_data.copy()
    invalid_data["imap_server"] = None
    invalid_data["password"] = None
    with pytest.raises(ValidationError) as exc_info:
        ParseSourceRequest(**invalid_data)
    error_str = str(exc_info.value)
    assert "Fields imap_server, password are required for email provider" in error_str


def test_invalid_provider_type():
    invalid_data = {
        "provider_type": "invalid_type",
        "sub_provider_type": SourceSubProviderType.default,
        "name": "Test Invalid",
        "start_time": None,
    }
    with pytest.raises(ValidationError) as exc_info:
        ParseSourceRequest(**invalid_data)
    assert "Invalid enum value" in str(exc_info.value)
