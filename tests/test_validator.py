from enum import Enum
from typing import Optional
from pydantic import BaseModel, field_validator, model_validator, ValidationError
import pytest


class TestSourceProviderType(str, Enum):
    RSS = "rss"
    WEBSITE = "website"
    EMAIL = "email"


class TestCreateProviderRequest(BaseModel):
    provider_type: TestSourceProviderType
    url: Optional[str] = None
    imap_server: Optional[str] = None
    imap_port: Optional[int] = None
    user: Optional[str] = None
    password: Optional[str] = None
    name: str = "test"

    @model_validator(mode='after')
    def validate_fields(self) -> 'TestCreateProviderRequest':
        if self.provider_type in [TestSourceProviderType.rss, TestSourceProviderType.website]:
            if not self.url:
                raise ValueError(f"URL is required for {self.provider_type.value} provider")
                
        if self.provider_type == TestSourceProviderType.email:
            required_fields = {
                'imap_server': self.imap_server,
                'imap_port': self.imap_port,
                'user': self.user,
                'password': self.password
            }
            missing_fields = [k for k, v in required_fields.items() if not v]
            if missing_fields:
                raise ValueError(f"Fields {', '.join(missing_fields)} are required for email provider")
                
        return self


def test_rss_source():
    # Test valid RSS provider
    data = {
        "provider_type": TestSourceProviderType.rss,
        "url": "http://example.com/rss",
        "name": "test"
    }
    provider = TestCreateProviderRequest(**data)
    assert provider.provider_type == TestSourceProviderType.rss
    assert provider.url == "http://example.com/rss"

    # Test invalid RSS provider (missing url)
    invalid_data = {
        "provider_type": TestSourceProviderType.rss,
        "name": "test"
    }
    with pytest.raises(ValidationError) as exc_info:
        TestCreateProviderRequest(**invalid_data)
    assert "URL is required for rss provider" in str(exc_info.value)


def test_email_source():
    # Test valid email provider
    valid_data = {
        "provider_type": TestSourceProviderType.email,
        "name": "test",
        "imap_server": "imap.example.com",
        "imap_port": 993,
        "user": "<EMAIL>",
        "password": "password123"
    }
    provider = TestCreateProviderRequest(**valid_data)
    assert provider.provider_type == TestSourceProviderType.email
    assert provider.imap_server == "imap.example.com"

    # Test invalid email provider (missing required fields)
    invalid_data = {
        "provider_type": TestSourceProviderType.email,
        "name": "test"
    }
    with pytest.raises(ValidationError) as exc_info:
        TestCreateProviderRequest(**invalid_data)
    error_str = str(exc_info.value)
    assert "Fields imap_server, imap_port, user, password are required for email provider" in error_str


if __name__ == "__main__":
    pytest.main([__file__, "-v"])