# Python-generated files
**/__pycache__/
**/*.py[oc]
**/build/
**/dist/
**/wheels/
**/*.egg-info

# database file
**/*.db
*.db
celerybeat-schedule.db
**/*.log

# Build
build/
dist/
*.egg-info/

# Celery
celerybeat-schedule.bak
celerybeat-schedule.dat
celerybeat-schedule.dir
celerybeat-schedule.db
*.pid

# Virtual environments
**/.venv

# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
**/node_modules
**/.pnp
**/.pnp.js
**/.yarn/install-state.gz

# testing
**/coverage

# next.js
**/.next/
**/out/

# production
**/build

# misc
**/.DS_Store
**/*.pem

# debug
**/npm-debug.log*
**/yarn-debug.log*
**/yarn-error.log*

# local env files
**/.env*.local

# vercel
**/.vercel

# typescript
**/*.tsbuildinfo
**/next-env.d.ts
backend/celerybeat-schedule.db
