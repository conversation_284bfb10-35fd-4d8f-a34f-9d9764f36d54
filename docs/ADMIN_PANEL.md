# Admin Panel Documentation

## Overview

The EyeSaver Admin Panel has been redesigned with a modern UI/UX approach, combining both API and web interfaces into a single streamlined codebase. This document outlines the changes made and how to use the new features.

## Key Changes

1. **Codebase Consolidation**
   - Merged `admin_api` and `admin_web` modules into a single module in `admin_web`
   - All API endpoints are now accessible through the `admin_web` module
   - API endpoints are prefixed with `/admin/api/` to differentiate them from web UI routes

2. **Modern UI**
   - Redesigned with a clean, responsive interface
   - Added animations and transitions
   - Improved navigation experience on both desktop and mobile devices
   - Enhanced card and table styles

3. **New Features**
   - Table sorting
   - Form validation
   - Modern confirmation dialogs
   - Tooltips
   - Improved notifications
   - API interaction capabilities for frontend
   - Mobile-friendly responsive design

## File Structure

```
app/
├── admin_web/
│   ├── __init__.py          # Combined router setup
│   └── admin_panel.py       # Combined API and web UI code
├── static/
│   ├── css/
│   │   └── admin.css        # Enhanced CSS with modern UI components
│   └── js/
│       └── admin.js         # Enhanced JavaScript with modern functionality
└── templates/
    └── admin/
        ├── layout.html      # Base template
        ├── login.html       # Login page
        ├── dashboard.html   # Admin dashboard
        ├── users.html       # User management
        ├── user_detail.html # User details
        ├── user_form.html   # User edit/create form
        └── admins.html      # Admin users list
```

## API Endpoints

All API endpoints now use the `/admin/api/` prefix:

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/admin/api/admins` | GET | List all admin users |
| `/admin/api/admins` | POST | Add a new admin user |
| `/admin/api/admins/{admin_id}` | DELETE | Remove admin privileges from a user |
| `/admin/api/users` | GET | List all users |
| `/admin/api/users/{user_id}` | GET | Get user details by ID |
| `/admin/api/users/{user_id}` | DELETE | Delete a user |

## Web UI Routes

| Route | Description |
|-------|-------------|
| `/admin` | Admin dashboard |
| `/admin/login` | Login page |
| `/admin/logout` | Logout action |
| `/admin/users` | User management page |
| `/admin/users/{user_id}` | User details page |
| `/admin/users/{user_id}/edit` | Edit user form |
| `/admin/users/new` | Create new user form |
| `/admin/admins` | Admin users list |

## Using the API from JavaScript

The admin panel now supports API interactions directly from the frontend using data attributes:

```html
<button 
  data-api-action="delete" 
  data-api-endpoint="/admin/api/users/123" 
  data-api-method="DELETE"
  data-confirm="Are you sure you want to delete this user?"
  data-reload="true"
  class="btn btn-danger">
  Delete User
</button>
```

## Authentication

The admin panel supports two authentication methods:

1. **Cookie-based authentication** - Used by the web UI
2. **Bearer token authentication** - Used for API requests

Both methods use the same token format and can be used interchangeably.

## Mobile Responsiveness

The admin panel is now fully responsive:

- Adaptive navigation menu that converts to a hamburger menu on small screens
- Responsive tables that adjust to screen size
- Fluid grid layouts for dashboard components
- Touch-friendly interactive elements

## UI Components

### Cards

```html
<div class="stat-card">
  <h3>Total Users</h3>
  <div class="number">250</div>
  <div class="actions">
    <a href="/admin/users" class="btn btn-primary btn-sm">View Users</a>
  </div>
</div>
```

### Tables

```html
<div class="table-container">
  <table class="admin-table">
    <thead>
      <tr>
        <th>ID</th>
        <th>Username</th>
        <th>Email</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <!-- Table rows here -->
    </tbody>
  </table>
</div>
```

### Badges

```html
<span class="badge badge-primary">Admin</span>
<span class="badge badge-success">Active</span>
<span class="badge badge-warning">Pending</span>
<span class="badge badge-info">Info</span>
```

### Buttons

```html
<button class="btn btn-primary">Primary</button>
<button class="btn btn-danger">Danger</button>
<button class="btn btn-success">Success</button>
<button class="btn btn-sm">Small Button</button>
```

### Forms

```html
<div class="form-container">
  <div class="form-group">
    <label for="username">Username</label>
    <input type="text" class="form-control" id="username" required>
  </div>
</div>
```

## Customization

The admin panel uses CSS variables for easy customization. You can modify the theme by changing the variables in `admin.css`:

```css
:root {
  --primary-color: #4361ee;
  --primary-hover: #3a56e4;
  --secondary-color: #f8f9fa;
  --text-color: #1a202c;
  /* Other variables */
}
``` 